import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld/sequence"
CLEAN_TEXT = "Product Requirements Document: E-commerce Platform\n... (shortened for brevity) ..."
STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, and payments."

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        # Get narrative first
        n_resp = await client.post("http://localhost:8000/api/hld/narrative", json={
            "clean_text": CLEAN_TEXT,
            "structured_prd": STRUCTURED_PRD
        })
        n_data = n_resp.json()
        hld_narrative = n_data.get("hld_narrative", "")
        print("/hld/narrative response:", n_data)
        print("\n--- Testing /hld/sequence ---")
        resp = await client.post(API_URL, json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": hld_narrative
        })
        data = resp.json()
        print("/hld/sequence response:", data)
        ok = data.get("status") == "success" and "mermaid" in data
        if ok:
            print("[PASS] /hld/sequence - mermaid present.")
        else:
            print(f"[FAIL] /hld/sequence - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 