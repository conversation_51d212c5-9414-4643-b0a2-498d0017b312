from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.routes import upload, query_prd, test_summariser
from app.routes import hld_routes
from app.routes import lld_routes
from app.auth.auth_router import router as auth_router
from app.auth.simple_auth_router import router as simple_auth_router
from dotenv import load_dotenv
from app.routes.supabase_health import router as supabase_health_router

load_dotenv()

app = FastAPI(
    title="AutoSpec Architect API",
    description="SaaS backend for PRD-to-HLD/LLD generation using AI and RAG",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# CORS configuration
origins = [
    "http://localhost:5173",
    "http://localhost:3000",
    "https://autospec-ui.vercel.app",
    "https://autospec-design.vercel.app",  # Frontend domain
    "https://autospec-design.netlify.app",  # Alternative frontend
    "*",  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check
@app.get("/", tags=["Health"])
async def root():
    return {"status": "OK", "message": "AutoSpec Architect backend is live"}


@app.get("/health", tags=["Health"])
async def health_check():
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "version": "1.0.0"
    }


# Include routers
app.include_router(auth_router, prefix="/api", tags=["Authentication"])  # ✅ Auth enabled
app.include_router(simple_auth_router, prefix="/api", tags=["Simple Auth"])  # ✅ Simple auth for testing
app.include_router(upload.router, prefix="/api/upload", tags=["Upload"])
app.include_router(query_prd.router, prefix="/api/query", tags=["Query"])
app.include_router(test_summariser.router, tags=["Test Summariser"])
app.include_router(hld_routes.router, prefix="/api/hld", tags=["HLD"])
app.include_router(lld_routes.router, prefix="/api/lld", tags=["LLD"])
app.include_router(supabase_health_router, prefix="/api")
