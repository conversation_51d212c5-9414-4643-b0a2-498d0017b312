import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Keys and Model Settings
TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY")
TOGETHER_MODEL = os.getenv(
    "TOGETHER_MODEL", "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
)
TOGETHER_BASE_URL = os.getenv("TOGETHER_BASE_URL", "https://api.together.xyz")

# JWT Secret (with default for demo purposes)
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key-change-in-production-use-at-least-32-characters")

# File limits
MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", 50))
MAX_CLEAN_TEXT_CHARS = int(os.getenv("MAX_CLEAN_TEXT_CHARS", 4000))

# Paths
TEMP_DIR = os.getenv("TEMP_DIR", "/tmp")
UPLOAD_DIR = os.getenv("UPLOAD_DIR", os.path.join(TEMP_DIR, "uploads"))

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

# LLM Configuration
LLM_CONFIG = {
    "model": TOGETHER_MODEL,
    "temperature": 0.2,
    "max_tokens": 2048,  # Increased to fix warning
    "together_api_key": TOGETHER_API_KEY,
}
