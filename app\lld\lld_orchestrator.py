import logging
import json
from typing import Dict, List, Any
from app.lld.class_structure_generator import generate_class_structure, get_fallback_class_structure
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import re

logger = logging.getLogger(__name__)

# --- Database Schema Generation ---
DB_SCHEMA_PROMPT = """
You are a senior database architect. Based on the PRD summary and HLD components, design a normalized relational database schema for PostgreSQL.

Requirements:
- List tables, columns, types, constraints, indexes, and relationships
- If unsure, make your best guess based on the PRD and components. If you cannot find enough, invent plausible tables for a typical system.
- Do not include explanations, markdown, or code blocks. Just return a JSON object as shown in the example.

Example:
{
  "database_type": "PostgreSQL",
  "tables": [
    {
      "name": "users",
      "description": "User account information",
      "columns": [
        {"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique user identifier"}
      ],
      "indexes": [],
      "relationships": []
    }
  ],
  "ddl_statements": ["CREATE TABLE users (id UUID PRIMARY KEY);"]
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

DB_SCHEMA_FALLBACK = {
    "database_type": "PostgreSQL",
    "tables": [
        {
            "name": "users",
            "description": "User account information",
            "columns": [
                {"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Unique user identifier"},
                {"name": "email", "type": "VARCHAR(255)", "constraints": ["UNIQUE"], "description": "User email address"}
            ],
            "indexes": [],
            "relationships": []
        },
        {
            "name": "products",
            "description": "Product catalog",
            "columns": [
                {"name": "id", "type": "UUID", "constraints": ["PRIMARY KEY"], "description": "Product ID"},
                {"name": "name", "type": "VARCHAR(255)", "constraints": [], "description": "Product name"}
            ],
            "indexes": [],
            "relationships": []
        }
    ],
    "ddl_statements": [
        "CREATE TABLE users (id UUID PRIMARY KEY, email VARCHAR(255) UNIQUE);",
        "CREATE TABLE products (id UUID PRIMARY KEY, name VARCHAR(255));"
    ]
}

# --- DML Statements Generation ---
DML_PROMPT = """
You are a database expert. Given the following database schema, generate sample DML statements for PostgreSQL.

Requirements:
- For each table, provide sample INSERT, UPDATE, DELETE, and a sample SELECT query.
- If unsure, make your best guess. If you cannot find enough, invent plausible statements for a typical system.
- Do not include explanations, markdown, or code blocks. Just return a JSON object as shown in the example.

Example:
{
  "insert_statements": [
    {"table": "users", "description": "Create a new user", "sql": "INSERT INTO users (id, email) VALUES (gen_random_uuid(), '<EMAIL>');"}
  ],
  "update_statements": [
    {"table": "users", "description": "Update user email", "sql": "UPDATE users SET email = '<EMAIL>' WHERE id = '...';"}
  ],
  "delete_statements": [
    {"table": "users", "description": "Delete a user", "sql": "DELETE FROM users WHERE id = '...';"}
  ],
  "sample_queries": [
    {"table": "users", "description": "Get all users", "sql": "SELECT * FROM users;"}
  ]
}

Database Schema: {database_schema}
"""

DML_FALLBACK = {
    "insert_statements": [
        {"table": "users", "description": "Create a new user", "sql": "INSERT INTO users (id, email) VALUES (gen_random_uuid(), '<EMAIL>');"}
    ],
    "update_statements": [
        {"table": "users", "description": "Update user email", "sql": "UPDATE users SET email = '<EMAIL>' WHERE id = '...';"}
    ],
    "delete_statements": [
        {"table": "users", "description": "Delete a user", "sql": "DELETE FROM users WHERE id = '...';"}
    ],
    "sample_queries": [
        {"table": "users", "description": "Get all users", "sql": "SELECT * FROM users;"}
    ]
}

def extract_first_json_object(text: str, required_keys=None, fallback=None):
    try:
        cleaned = text.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)
        cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        match = re.search(r"\{.*?\}", cleaned, re.DOTALL)
        if not match:
            logger.error(f"No JSON object found in LLM output. Raw response: {text}")
            return fallback if fallback is not None else {}
        json_str = match.group(0)
        try:
            parsed = json.loads(json_str)
            if required_keys:
                missing = [k for k in required_keys if k not in parsed]
                if missing:
                    logger.error(f"Missing keys: {missing}")
                    return fallback if fallback is not None else {}
            return parsed
        except Exception as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"JSON string: {json_str}")
            logger.error(f"Raw LLM response: {text}")
            return fallback if fallback is not None else {}
    except Exception as e:
        logger.error(f"Error during JSON object extraction: {str(e)}")
        logger.error(f"Raw LLM response: {text}")
        return fallback if fallback is not None else {}

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type(Exception))
async def generate_lld_database_schema(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    from app.config import TOGETHER_MODEL, TOGETHER_API_KEY
    from langchain_together import Together
    llm = Together(model=TOGETHER_MODEL, temperature=0.2, max_tokens=2048, together_api_key=TOGETHER_API_KEY)
    prompt = DB_SCHEMA_PROMPT.format(prd_summary=prd_summary, hld_components=json.dumps(hld_components, indent=2))
    try:
        print("[DEBUG] About to call LLM for /database-schema (fresh)")
        try:
            response = await llm.ainvoke(prompt)
        except Exception as e:
            print("[DEBUG] Exception during LLM call for /database-schema (fresh):", repr(e))
            raise
        print("[DEBUG] LLM call finished for /database-schema (fresh)")
        print("[DEBUG] Raw LLM output for /database-schema (fresh):\n", response)
        logger.info(f"DB schema LLM response (fresh): {response}")
        return extract_first_json_object(response, required_keys=["database_type", "tables", "ddl_statements"], fallback=DB_SCHEMA_FALLBACK)
    except Exception as e:
        logger.error(f"Failed to generate DB schema (fresh): {e}")
        return DB_SCHEMA_FALLBACK

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type(Exception))
async def generate_lld_dml_statements(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    schema = await generate_lld_database_schema(prd_summary, hld_components)
    from app.config import TOGETHER_MODEL, TOGETHER_API_KEY
    from langchain_together import Together
    llm = Together(model=TOGETHER_MODEL, temperature=0.2, max_tokens=2048, together_api_key=TOGETHER_API_KEY)
    prompt = DML_PROMPT.format(database_schema=json.dumps(schema, indent=2))
    try:
        print("[DEBUG] About to call LLM for /dml-statements (fresh)")
        try:
            response = await llm.ainvoke(prompt)
        except Exception as e:
            print("[DEBUG] Exception during LLM call for /dml-statements (fresh):", repr(e))
            raise
        print("[DEBUG] LLM call finished for /dml-statements (fresh)")
        print("[DEBUG] Raw LLM output for /dml-statements (fresh):\n", response)
        logger.info(f"DML LLM response (fresh): {response}")
        return extract_first_json_object(response, required_keys=["insert_statements", "update_statements", "delete_statements", "sample_queries"], fallback=DML_FALLBACK)
    except Exception as e:
        logger.error(f"Failed to generate DML statements (fresh): {e}")
        return DML_FALLBACK

# --- Complete LLD Generation ---
from datetime import datetime

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type(Exception))
async def generate_complete_lld(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    db_schema = await generate_lld_database_schema(prd_summary, hld_components)
    class_structure = await generate_class_structure(prd_summary, hld_components)
    dml_statements = await generate_lld_dml_statements(prd_summary, hld_components)
    return {
        "database_schema": db_schema,
        "class_structure": class_structure,
        "dml_statements": dml_statements,
        "generated_at": datetime.utcnow().isoformat() + "Z"
    } 