import logging
import asyncio
from typing import Dict, List, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from langchain_together import Together
from app.utils.vector_store import vector_store
from app.config import LLM_CONFIG

logger = logging.getLogger(__name__)

class RAGService:
    """
    Service class for handling RAG (Retrieval-Augmented Generation) operations.
    
    This service encapsulates the business logic for:
    - Document querying and retrieval
    - Context building and response generation
    - Search operations
    - Document management
    """
    
    def __init__(self):
        self.llm = Together(**LLM_CONFIG)
        
        # RAG Prompt Template
        self.rag_prompt = """
You are an expert system design assistant with access to Product Requirements Documents (PRD).

Context from PRD:
{context}

User Question: {question}

Instructions:
- Answer based ONLY on the provided PRD context
- Be specific and reference relevant parts of the PRD
- If the information is not in the context, clearly state that
- Provide clear, actionable insights
- Keep responses concise but comprehensive
- Use bullet points for multiple items
- Reference specific sections when possible

Answer:
"""
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    async def generate_rag_response(self, question: str, context: str) -> str:
        """
        Generate RAG-enhanced response using retrieved context.
        
        Args:
            question: User's question
            context: Retrieved context from vector store
            
        Returns:
            Generated response
        """
        try:
            prompt = self.rag_prompt.format(question=question, context=context)
            response = await self.llm.ainvoke(prompt)
            return response.strip()
        except Exception as e:
            logger.error(f"RAG response generation failed: {e}")
            raise
    
    def build_context_from_results(self, search_results: List[Dict[str, Any]]) -> str:
        """
        Build context string from search results.
        
        Args:
            search_results: List of search results from vector store
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        for i, result in enumerate(search_results, 1):
            content = result.get("content", "")
            score = result.get("score", 0)
            metadata = result.get("metadata", {})
            title = metadata.get("title", "Unknown Document")
            
            context_parts.append(
                f"[Source {i}: {title} (Relevance: {score:.3f})]\n{content}\n"
            )
        
        return "\n---\n".join(context_parts)
    
    async def query_documents(self, question: str, limit: int = 5, 
                            document_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Query documents using RAG.
        
        Args:
            question: User's question
            limit: Maximum number of chunks to retrieve
            document_id: Optional specific document ID
            
        Returns:
            Dictionary with answer, sources, and metadata
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing RAG query: {question}")
            
            # Step 1: Retrieve relevant context
            search_results = vector_store.search_similar(
                question, 
                limit=limit,
                document_id=document_id
            )
            
            if not search_results:
                raise ValueError("No relevant documents found. Please upload a PRD first.")
            
            logger.info(f"Retrieved {len(search_results)} relevant chunks")
            
            # Step 2: Build context
            context = self.build_context_from_results(search_results)
            
            # Step 3: Generate response
            answer = await self.generate_rag_response(question, context)
            
            # Step 4: Calculate processing time
            processing_time = int((time.time() - start_time) * 1000)
            
            # Step 5: Prepare sources
            sources = [
                {
                    "title": result.get("metadata", {}).get("title", "Unknown"),
                    "score": result.get("score", 0),
                    "content_preview": result.get("content", "")[:200] + "...",
                    "document_id": result.get("document_id", ""),
                    "chunk_id": result.get("chunk_id", 0)
                }
                for result in search_results
            ]
            
            return {
                "answer": answer,
                "sources": sources,
                "chunks_retrieved": len(search_results),
                "processing_time_ms": processing_time
            }
            
        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            raise
    
    async def search_documents(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search documents without generating RAG response.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            results = vector_store.search_similar(query, limit=limit)
            
            return [
                {
                    "content": result.get("content", ""),
                    "score": result.get("score", 0),
                    "metadata": result.get("metadata", {}),
                    "document_id": result.get("document_id", ""),
                    "chunk_id": result.get("chunk_id", 0)
                }
                for result in results
            ]
            
        except Exception as e:
            logger.error(f"Document search failed: {e}")
            raise
    
    def list_documents(self) -> List[Dict[str, Any]]:
        """
        List all documents in the vector store.
        
        Returns:
            List of document metadata
        """
        try:
            return vector_store.list_documents()
        except Exception as e:
            logger.error(f"Failed to list documents: {e}")
            raise
    
    def get_document_chunks(self, document_id: str) -> List[Dict[str, Any]]:
        """
        Get all chunks for a specific document.
        
        Args:
            document_id: Document ID
            
        Returns:
            List of document chunks
        """
        try:
            return vector_store.get_document_chunks(document_id)
        except Exception as e:
            logger.error(f"Failed to get document chunks: {e}")
            raise
    
    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document and all its chunks.
        
        Args:
            document_id: Document ID to delete
            
        Returns:
            True if successful
        """
        try:
            return vector_store.delete_document(document_id)
        except Exception as e:
            logger.error(f"Failed to delete document: {e}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get vector store collection statistics.
        
        Returns:
            Dictionary with collection stats
        """
        try:
            return vector_store.get_collection_stats()
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on RAG system.
        
        Returns:
            Health status information
        """
        try:
            # Check vector store
            documents = self.list_documents()
            
            # Check LLM
            test_response = await self.llm.ainvoke("Hello")
            
            return {
                "status": "healthy",
                "vector_store": "connected",
                "llm": "connected",
                "documents_count": len(documents),
                "collection_stats": self.get_collection_stats()
            }
        except Exception as e:
            logger.error(f"RAG health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }

    async def process_rag_query(self, question: str, limit: int = 5, include_sources: bool = True) -> Dict[str, Any]:
        """Process a RAG query with improved error handling and response formatting."""
        start_time = time.time()
        
        try:
            # Search for relevant documents
            search_results = vector_store.search_similar(question, limit=limit)
            
            if not search_results:
                return {
                    "answer": "I couldn't find any relevant information in the uploaded documents. Please make sure you have uploaded a PRD document first.",
                    "sources": [],
                    "processing_time_ms": int((time.time() - start_time) * 1000),
                    "confidence_score": 0.0,
                    "status": "no_documents"
                }
            
            # Build context from search results
            context = self.build_context_from_results(search_results)
            
            # Generate response using LLM
            response = await self.generate_rag_response(question, context)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # Calculate confidence score based on source relevance
            avg_score = sum(result.get('score', 0) for result in search_results) / len(search_results)
            confidence_score = min(avg_score, 1.0)
            
            return {
                "answer": response,
                "sources": search_results if include_sources else [],
                "processing_time_ms": processing_time,
                "confidence_score": confidence_score,
                "status": "success",
                "metadata": {
                    "sources_count": len(search_results),
                    "avg_relevance_score": avg_score,
                    "query_complexity": self._assess_query_complexity(question)
                }
            }
            
        except Exception as e:
            logger.error(f"RAG query processing failed: {e}")
            return {
                "answer": "I encountered an error while processing your query. Please try again.",
                "sources": [],
                "processing_time_ms": int((time.time() - start_time) * 1000),
                "confidence_score": 0.0,
                "status": "error",
                "error": str(e)
            }

    def _assess_query_complexity(self, question: str) -> str:
        """Assess the complexity of the query for frontend display."""
        word_count = len(question.split())
        if word_count <= 3:
            return "simple"
        elif word_count <= 8:
            return "moderate"
        else:
            return "complex"

# Global instance
rag_service = RAGService() 