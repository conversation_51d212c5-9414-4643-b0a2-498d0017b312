{"data_mtime": 1753397832, "dep_lines": [14, 15, 16, 17, 19, 18, 6, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.utils.text_extractor", "app.llm.summariser", "app.llm.parser", "app.llm.refiner", "app.auth.auth_guard", "app.config", "logging", "os", "shutil", "time", "uuid", "<PERSON><PERSON><PERSON>", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "contextlib", "enum", "fastapi.datastructures", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "io", "pydantic", "pydantic.networks", "starlette", "starlette.datastructures", "starlette.responses", "starlette.routing", "types", "typing", "warnings"], "hash": "827ca0b97ecfc5abffd84644a92c169454570d29", "id": "app.routes.upload", "ignore_all": false, "interface_hash": "c344fb0d380ed6e2cb8ee45445439f0988f24807", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\AutoSpecDesign\\app\\routes\\upload.py", "plugin_data": null, "size": 5544, "suppressed": [], "version_id": "1.15.0"}