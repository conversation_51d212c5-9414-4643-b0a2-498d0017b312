import hashlib
import uuid
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>
# Remove any remaining import or reference to supabase_service or database logic.
# Only keep in-memory user logic.
import jwt
from app.config import JWT_SECRET

class SimpleAuth:
    def __init__(self):
        self.users = {}  # In-memory user store for demo
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_user(self, email: str, password: str, full_name: str = None) -> dict:
        """Create a new user."""
        # Check if user already exists
        if email in self.users:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Hash password
        hashed_password = self.hash_password(password)
        
        # Create user
        user_id = str(uuid.uuid4())
        user = {
            "user_id": user_id,
            "email": email,
            "password_hash": hashed_password,
            "full_name": full_name,
            "role": "user",
            "subscription_tier": "free",
            "created_at": datetime.utcnow()
        }
        
        # Store in memory
        self.users[email] = user
        
        # Also save to database if available
        # Remove all supabase_service usage and database persistence logic
        # ... keep only in-memory user logic ...
        
        return user
    
    def authenticate_user(self, email: str, password: str) -> dict:
        """Authenticate user with email and password."""
        # Check if user exists
        if email not in self.users:
            raise HTTPException(status_code=401, detail="Invalid email or password")
        
        user = self.users[email]
        
        # Verify password
        hashed_password = self.hash_password(password)
        if user["password_hash"] != hashed_password:
            raise HTTPException(status_code=401, detail="Invalid email or password")
        
        return user
    
    def create_token(self, user: dict) -> str:
        """Create JWT token for user."""
        payload = {
            "sub": user["user_id"],
            "email": user["email"],
            "role": user["role"],
            "exp": datetime.utcnow() + timedelta(hours=24),
            "iat": datetime.utcnow()
        }
        
        return jwt.encode(payload, JWT_SECRET, algorithm="HS256")
    
    def verify_token(self, token: str) -> dict:
        """Verify JWT token and return user info."""
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
            user_id = payload.get("sub")
            email = payload.get("email")
            
            # Find user by email
            for user in self.users.values():
                if user["user_id"] == user_id and user["email"] == email:
                    return user
            
            raise HTTPException(status_code=401, detail="Invalid token")
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")

# Global instance
simple_auth = SimpleAuth() 