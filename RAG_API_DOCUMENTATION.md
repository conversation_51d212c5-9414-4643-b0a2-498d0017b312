# RAG (Retrieval-Augmented Generation) API Documentation

## 🎯 Overview

The RAG system allows users to query their uploaded PRD documents using natural language questions and receive context-aware responses. This combines vector search with AI generation to provide accurate, relevant answers based on the actual content of your PRD documents.

## 🔗 Base URL
```
http://localhost:8000/api/query
```

## 📋 RAG Endpoints

### 1. Query PRD Documents (Main RAG Endpoint)
```http
POST /api/query/
```

**Request Body:**
```json
{
  "question": "What are the main features of this system?",
  "document_id": "optional-specific-document-id",
  "limit": 5
}
```

**Response:**
```json
{
  "status": "success",
  "question": "What are the main features of this system?",
  "answer": "Based on the PRD, the main features include: 1) User authentication system with JWT tokens, 2) Document upload and processing capabilities, 3) AI-powered HLD/LLD generation, 4) Vector-based document search, and 5) Real-time collaboration features. The system is designed as a SaaS platform for technical teams to convert PRDs into comprehensive system designs.",
  "context_sources": [
    {
      "title": "E-commerce Platform PRD",
      "score": 0.892,
      "content_preview": "The system shall provide user authentication with JWT tokens..."
    },
    {
      "title": "E-commerce Platform PRD", 
      "score": 0.756,
      "content_preview": "Main features include: 1) User management..."
    }
  ],
  "chunks_retrieved": 5
}
```

### 2. Search Documents Only (Debugging)
```http
POST /api/query/search-only
```

**Request Body:**
```json
{
  "query": "authentication system",
  "limit": 3
}
```

**Response:**
```json
{
  "status": "success",
  "query": "authentication system",
  "results": [
    {
      "content": "The authentication system shall use JWT tokens...",
      "score": 0.892,
      "metadata": {
        "title": "E-commerce Platform PRD",
        "filename": "prd.pdf",
        "document_id": "uuid-123"
      }
    }
  ]
}
```

### 3. List All Documents
```http
GET /api/query/documents
```

**Response:**
```json
{
  "status": "success",
  "documents": [
    {
      "document_id": "uuid-123",
      "title": "E-commerce Platform PRD",
      "filename": "prd.pdf",
      "file_size": 245760,
      "chunk_count": 15
    },
    {
      "document_id": "uuid-456", 
      "title": "Mobile App PRD",
      "filename": "mobile-prd.docx",
      "file_size": 189440,
      "chunk_count": 12
    }
  ],
  "total_documents": 2
}
```

### 4. Get Document Chunks
```http
GET /api/query/documents/{document_id}/chunks
```

**Response:**
```json
{
  "status": "success",
  "document_id": "uuid-123",
  "chunks": [
    {
      "chunk_id": 0,
      "content": "The system shall provide user authentication...",
      "metadata": {
        "title": "E-commerce Platform PRD",
        "filename": "prd.pdf"
      }
    },
    {
      "chunk_id": 1,
      "content": "Main features include: 1) User management...",
      "metadata": {
        "title": "E-commerce Platform PRD",
        "filename": "prd.pdf"
      }
    }
  ],
  "total_chunks": 15
}
```

## 🎨 Frontend Integration Examples

### React/Next.js Example

```javascript
// RAG Query Component
import { useState } from 'react';

const RAGQueryComponent = () => {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [sources, setSources] = useState([]);

  const queryPRD = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/query/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question,
          limit: 5
        })
      });
      
      const data = await response.json();
      
      if (data.status === 'success') {
        setAnswer(data.answer);
        setSources(data.context_sources);
      } else {
        console.error('Query failed:', data.message);
      }
    } catch (error) {
      console.error('Query error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="rag-query-container">
      <div className="query-input">
        <textarea
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Ask a question about your PRD..."
          rows={3}
        />
        <button 
          onClick={queryPRD}
          disabled={loading || !question.trim()}
        >
          {loading ? 'Querying...' : 'Ask Question'}
        </button>
      </div>
      
      {answer && (
        <div className="answer-section">
          <h3>Answer:</h3>
          <p>{answer}</p>
          
          <h4>Sources:</h4>
          <div className="sources">
            {sources.map((source, index) => (
              <div key={index} className="source-item">
                <strong>{source.title}</strong>
                <span>Relevance: {(source.score * 100).toFixed(1)}%</span>
                <p>{source.content_preview}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RAGQueryComponent;
```

### Vue.js Example

```javascript
// Composition API
import { ref } from 'vue'

export function useRAGQuery() {
  const question = ref('')
  const answer = ref('')
  const sources = ref([])
  const loading = ref(false)

  const queryPRD = async () => {
    loading.value = true
    
    try {
      const response = await fetch('http://localhost:8000/api/query/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.value,
          limit: 5
        })
      })
      
      const data = await response.json()
      
      if (data.status === 'success') {
        answer.value = data.answer
        sources.value = data.context_sources
      } else {
        console.error('Query failed:', data.message)
      }
    } catch (error) {
      console.error('Query error:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    question,
    answer,
    sources,
    loading,
    queryPRD
  }
}
```

## 🔧 How RAG Works

### 1. Document Processing
When you upload a PRD:
1. **Text Extraction** - Clean text is extracted from PDF/DOCX files
2. **Chunking** - Text is split into smaller chunks (1000 chars with 200 char overlap)
3. **Embedding** - Each chunk is converted to a vector embedding
4. **Storage** - Chunks are stored in Qdrant vector database with metadata

### 2. Query Processing
When you ask a question:
1. **Query Embedding** - Your question is converted to a vector
2. **Similarity Search** - Vector database finds most similar chunks
3. **Context Building** - Relevant chunks are combined into context
4. **AI Generation** - LLM generates answer using retrieved context
5. **Response** - Answer + source information is returned

### 3. RAG Prompt Structure
```
You are an expert system design assistant. You have access to a Product Requirements Document (PRD) and need to answer questions about it.

Context from PRD:
[Retrieved relevant chunks from vector store]

User Question: [Your question]

Instructions:
- Answer based ONLY on the provided PRD context
- Be specific and reference relevant parts of the PRD
- If the information is not in the context, say so
- Provide clear, actionable insights
- Keep responses concise but comprehensive

Answer:
[AI-generated response]
```

## 🚀 Best Practices

### 1. Question Formulation
- **Be Specific**: "What are the authentication requirements?" vs "Tell me about auth"
- **Ask for Details**: "What are the main features and their priorities?"
- **Request Examples**: "Give me examples of user roles and permissions"

### 2. Document Quality
- **Clear Structure**: Well-organized PRDs work better
- **Comprehensive Content**: More detailed PRDs provide better answers
- **Consistent Formatting**: Helps with text extraction

### 3. Performance Tips
- **Limit Results**: Use `limit` parameter to control context size
- **Specific Documents**: Use `document_id` for targeted queries
- **Error Handling**: Always handle API errors gracefully

## 🔍 Example Questions

### System Design Questions
- "What are the main components of this system?"
- "How does the authentication system work?"
- "What database technologies are recommended?"
- "What are the performance requirements?"

### Feature Questions
- "What features are included in the MVP?"
- "What are the user roles and permissions?"
- "How does the payment system work?"
- "What are the integration requirements?"

### Technical Questions
- "What is the recommended tech stack?"
- "What are the security requirements?"
- "How does the system handle scalability?"
- "What are the deployment requirements?"

## ⚠️ Limitations

1. **Context Window**: Limited by LLM context window size
2. **Embedding Quality**: Currently using simple hash-based embeddings
3. **Document Size**: Very large documents may be truncated
4. **Real-time Updates**: Changes require re-uploading documents

## 🔧 Future Improvements

1. **Better Embeddings**: Integrate OpenAI embeddings for better search
2. **Document Updates**: Support for updating existing documents
3. **Conversation History**: Maintain chat context across queries
4. **Multi-document Queries**: Query across multiple PRDs simultaneously
5. **Advanced Filtering**: Filter by document type, date, etc.

---

**Ready for intelligent PRD querying!** 🧠✨ 