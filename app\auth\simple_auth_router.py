from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr
from app.auth.simple_auth import simple_auth
from app.services.supabase_service import supabase_service
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# Request/Response Models
class UserSignupRequest(BaseModel):
    email: EmailStr
    password: str
    full_name: str = None

class UserLoginRequest(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    email: str
    role: str

@router.post("/auth/signup", response_model=TokenResponse)
async def signup_user(request: UserSignupRequest):
    """Sign up a new user with proper authentication."""
    try:
        # Create user with hashed password
        user = simple_auth.create_user(
            email=request.email,
            password=request.password,
            full_name=request.full_name
        )
        
        # Create access token
        access_token = simple_auth.create_token(user)
        
        return TokenResponse(
            access_token=access_token,
            user_id=user["user_id"],
            email=user["email"],
            role=user["role"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signup error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/auth/login", response_model=TokenResponse)
async def login_user(request: UserLoginRequest):
    """Login user with proper authentication."""
    try:
        # Authenticate user
        user = simple_auth.authenticate_user(request.email, request.password)
        
        # Create access token
        access_token = simple_auth.create_token(user)
        
        return TokenResponse(
            access_token=access_token,
            user_id=user["user_id"],
            email=user["email"],
            role=user["role"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/auth/me")
async def get_current_user_profile(token: str):
    """Get current user profile using token."""
    try:
        # Verify token and get user
        user = simple_auth.verify_token(token)
        
        # Get user profile from database if available
        if supabase_service.is_available():
            try:
                user_profile = await supabase_service.get_user_profile(user["user_id"])
                if "error" not in user_profile:
                    return user_profile
            except Exception as e:
                logger.warning(f"Could not fetch user profile from database: {e}")
        
        # Return basic user info if database not available
        return {
            "user_id": user["user_id"],
            "email": user["email"],
            "full_name": user["full_name"],
            "role": user["role"],
            "subscription_tier": user["subscription_tier"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile fetch error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/auth/verify")
async def verify_token(token: str):
    """Verify JWT token and return user info."""
    try:
        user = simple_auth.verify_token(token)
        return {
            "valid": True,
            "user_id": user["user_id"],
            "email": user["email"],
            "role": user["role"]
        }
    except HTTPException:
        return {"valid": False, "error": "Invalid token"}
    except Exception as e:
        return {"valid": False, "error": str(e)} 