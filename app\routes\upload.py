"""
Upload route for PRD document processing.

This module handles file uploads, text extraction, and PRD processing.
"""
import logging
import os
import shutil
import time
import uuid

from fastapi import APIRouter, UploadFile, Form, File

from app.utils.text_extractor import extract_text_from_file
from app.llm.summariser import summarise_prd
from app.llm.parser import parse_prd_content
from app.llm.refiner import refine_parsed_output as refine_parsed_fields
from app.config import MAX_CLEAN_TEXT_CHARS
from app.auth.auth_guard import OptionalUser

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/")
async def upload_prd(
    file: UploadFile = File(...),
    title: str = Form(...),
    user=OptionalUser
):
    """
    Upload and process a PRD document.

    Args:
        file: The uploaded file
        title: Document title
        user: Optional authenticated user

    Returns:
        JSON response with processing results
    """
    start_time = time.time()

    try:
        logger.info("Received file: %s with title: %s", file.filename, title)

        # Generate unique document ID
        document_id = str(uuid.uuid4())

        # Save the uploaded file temporarily
        file_location = f"temp_files/{file.filename}"
        os.makedirs("temp_files", exist_ok=True)
        with open(file_location, "wb+") as file_object:
            shutil.copyfileobj(file.file, file_object)

        file_size = os.path.getsize(file_location)

        # Extract clean text from file
        clean_text = extract_text_from_file(file_location)
        logger.info("Text extraction completed.")

        # Token-safe trimming
        clean_text = clean_text[:MAX_CLEAN_TEXT_CHARS]
        logger.info(
            "Clean text trimmed to %d characters.", MAX_CLEAN_TEXT_CHARS
        )

        # Add document to vector store for RAG
        chunks_added = 0
        try:
            # TODO: Add Supabase integration for document storage
            # metadata = {
            #     "title": title,
            #     "filename": file.filename,
            #     "document_id": document_id,
            #     "file_size": file_size,
            #     "user_id": user["user_id"] if user else "anonymous"
            # }
            # chunks_added = vector_store.add_document(document_id, clean_text, metadata)
            logger.info(
                "Added document to vector store with %d chunks", chunks_added
            )
        except Exception as e:
            logger.warning("Failed to add document to vector store: %s", e)
            # Continue processing even if vector store fails

        # Summarize the PRD to get structured output
        summary = await summarise_prd(clean_text)
        logger.info("PRD summarization completed.")

        # Parse and refine the PRD fields
        parsed_fields = await parse_prd_content(summary)
        # Ensure parsed_fields is a string for refine_parsed_fields
        if not isinstance(parsed_fields, str):
            parsed_fields_str = str(parsed_fields)
        else:
            parsed_fields_str = parsed_fields
        refined_fields = await refine_parsed_fields(parsed_fields_str, clean_text)
        logger.info("PRD parsing and refinement completed.")

        processing_time_ms = int((time.time() - start_time) * 1000)

        # Clean up temporary file
        try:
            os.remove(file_location)
            logger.info("Cleaned up temporary file: %s", file_location)
        except Exception as cleanup_error:
            logger.warning("Failed to clean up temporary file: %s", cleanup_error)

        return {
            "status": "success",
            "document_id": document_id,
            "file_name": file.filename,
            "title": title,
            "clean_text": clean_text,
            "structured_prd": refined_fields,
            "vector_store_chunks": chunks_added,
            "processing_time_ms": processing_time_ms,
            "authenticated": user is not None,
        }

    except Exception as e:
        logger.error("Upload processing failed: %s", str(e))
        return {"status": "error", "message": str(e)}


@router.post("/search")
async def search_documents(query: str, user=OptionalUser):
    """Search documents in the vector store (not implemented, no DB/vector store)."""
    # TODO: Implement document search with vector store
    _ = user  # Mark as used to avoid linting warnings
    return {
        "status": "success",
        "query": query,
        "results": [],
        "processing_time_ms": 0
    }


@router.get("/download/{document_id}")
async def download_document(document_id: str, user=OptionalUser):
    """Download a document file from storage (not implemented, no DB)."""
    # TODO: Implement document download from Supabase storage
    _ = document_id, user  # Mark as used to avoid linting warnings
    return {
        "status": "error",
        "message": "Download not supported in stateless mode."
    }


@router.delete("/{document_id}")
async def delete_document(document_id: str, user=OptionalUser):
    """Delete a document and its associated files (not implemented, no DB)."""
    # TODO: Implement document deletion from Supabase
    _ = document_id, user  # Mark as used to avoid linting warnings
    return {
        "status": "error",
        "message": "Delete not supported in stateless mode."
    }
