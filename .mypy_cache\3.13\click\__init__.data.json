{".class": "MypyFile", "_fullname": "click", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Abort": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.Abort", "kind": "Gdef"}, "Argument": {".class": "SymbolTableNode", "cross_ref": "click.core.Argument", "kind": "Gdef"}, "BOOL": {".class": "SymbolTableNode", "cross_ref": "click.types.BOOL", "kind": "Gdef"}, "BadArgumentUsage": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.BadArgumentUsage", "kind": "Gdef"}, "BadOptionUsage": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.BadOptionUsage", "kind": "Gdef"}, "BadParameter": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.BadParameter", "kind": "Gdef"}, "Choice": {".class": "SymbolTableNode", "cross_ref": "click.types.Choice", "kind": "Gdef"}, "ClickException": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.ClickException", "kind": "Gdef"}, "Command": {".class": "SymbolTableNode", "cross_ref": "click.core.Command", "kind": "Gdef"}, "CommandCollection": {".class": "SymbolTableNode", "cross_ref": "click.core.CommandCollection", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "click.core.Context", "kind": "Gdef"}, "DateTime": {".class": "SymbolTableNode", "cross_ref": "click.types.DateTime", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "click.types.FLOAT", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "click.types.File", "kind": "Gdef"}, "FileError": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.FileError", "kind": "Gdef"}, "FloatRange": {".class": "SymbolTableNode", "cross_ref": "click.types.FloatRange", "kind": "Gdef"}, "Group": {".class": "SymbolTableNode", "cross_ref": "click.core.Group", "kind": "Gdef"}, "HelpFormatter": {".class": "SymbolTableNode", "cross_ref": "click.formatting.HelpFormatter", "kind": "Gdef"}, "INT": {".class": "SymbolTableNode", "cross_ref": "click.types.INT", "kind": "Gdef"}, "IntRange": {".class": "SymbolTableNode", "cross_ref": "click.types.IntRange", "kind": "Gdef"}, "MissingParameter": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.MissingParameter", "kind": "Gdef"}, "NoSuchOption": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.NoSuchOption", "kind": "Gdef"}, "Option": {".class": "SymbolTableNode", "cross_ref": "click.core.Option", "kind": "Gdef"}, "ParamType": {".class": "SymbolTableNode", "cross_ref": "click.types.ParamType", "kind": "Gdef"}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "click.core.Parameter", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "click.types.Path", "kind": "Gdef"}, "STRING": {".class": "SymbolTableNode", "cross_ref": "click.types.STRING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "click.types.Tuple", "kind": "Gdef"}, "UNPROCESSED": {".class": "SymbolTableNode", "cross_ref": "click.types.UNPROCESSED", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "click.types.UUID", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "click.exceptions.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "argument": {".class": "SymbolTableNode", "cross_ref": "click.decorators.argument", "kind": "Gdef"}, "clear": {".class": "SymbolTableNode", "cross_ref": "click.termui.clear", "kind": "Gdef"}, "command": {".class": "SymbolTableNode", "cross_ref": "click.decorators.command", "kind": "Gdef"}, "confirm": {".class": "SymbolTableNode", "cross_ref": "click.termui.confirm", "kind": "Gdef"}, "confirmation_option": {".class": "SymbolTableNode", "cross_ref": "click.decorators.confirmation_option", "kind": "Gdef"}, "echo": {".class": "SymbolTableNode", "cross_ref": "click.utils.echo", "kind": "Gdef"}, "echo_via_pager": {".class": "SymbolTableNode", "cross_ref": "click.termui.echo_via_pager", "kind": "Gdef"}, "edit": {".class": "SymbolTableNode", "cross_ref": "click.termui.edit", "kind": "Gdef"}, "format_filename": {".class": "SymbolTableNode", "cross_ref": "click.utils.format_filename", "kind": "Gdef"}, "get_app_dir": {".class": "SymbolTableNode", "cross_ref": "click.utils.get_app_dir", "kind": "Gdef"}, "get_binary_stream": {".class": "SymbolTableNode", "cross_ref": "click.utils.get_binary_stream", "kind": "Gdef"}, "get_current_context": {".class": "SymbolTableNode", "cross_ref": "click.globals.get_current_context", "kind": "Gdef"}, "get_text_stream": {".class": "SymbolTableNode", "cross_ref": "click.utils.get_text_stream", "kind": "Gdef"}, "getchar": {".class": "SymbolTableNode", "cross_ref": "click.termui.getchar", "kind": "Gdef"}, "group": {".class": "SymbolTableNode", "cross_ref": "click.decorators.group", "kind": "Gdef"}, "help_option": {".class": "SymbolTableNode", "cross_ref": "click.decorators.help_option", "kind": "Gdef"}, "launch": {".class": "SymbolTableNode", "cross_ref": "click.termui.launch", "kind": "Gdef"}, "make_pass_decorator": {".class": "SymbolTableNode", "cross_ref": "click.decorators.make_pass_decorator", "kind": "Gdef"}, "open_file": {".class": "SymbolTableNode", "cross_ref": "click.utils.open_file", "kind": "Gdef"}, "option": {".class": "SymbolTableNode", "cross_ref": "click.decorators.option", "kind": "Gdef"}, "pass_context": {".class": "SymbolTableNode", "cross_ref": "click.decorators.pass_context", "kind": "Gdef"}, "pass_obj": {".class": "SymbolTableNode", "cross_ref": "click.decorators.pass_obj", "kind": "Gdef"}, "password_option": {".class": "SymbolTableNode", "cross_ref": "click.decorators.password_option", "kind": "Gdef"}, "pause": {".class": "SymbolTableNode", "cross_ref": "click.termui.pause", "kind": "Gdef"}, "progressbar": {".class": "SymbolTableNode", "cross_ref": "click.termui.progressbar", "kind": "Gdef"}, "prompt": {".class": "SymbolTableNode", "cross_ref": "click.termui.prompt", "kind": "Gdef"}, "secho": {".class": "SymbolTableNode", "cross_ref": "click.termui.secho", "kind": "Gdef"}, "style": {".class": "SymbolTableNode", "cross_ref": "click.termui.style", "kind": "Gdef"}, "unstyle": {".class": "SymbolTableNode", "cross_ref": "click.termui.unstyle", "kind": "Gdef"}, "version_option": {".class": "SymbolTableNode", "cross_ref": "click.decorators.version_option", "kind": "Gdef"}, "wrap_text": {".class": "SymbolTableNode", "cross_ref": "click.formatting.wrap_text", "kind": "Gdef"}}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\__init__.py"}