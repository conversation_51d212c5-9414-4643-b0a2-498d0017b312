from fastapi import APIRouter, Request, HTTPException, Depends
from pydantic import BaseModel, EmailStr
from app.auth.jwt_handler import decode_jwt_from_header
from app.auth.auth_guard import get_current_user, AuthenticatedUser
from app.services.supabase_service import supabase_service
import logging
import uuid
from datetime import datetime, timedelta
import jwt
from app.config import JWT_SECRET

router = APIRouter()
logger = logging.getLogger(__name__)

# Request/Response Models
class UserSignupRequest(BaseModel):
    email: EmailStr
    password: str
    full_name: str = None
    company: str = None

class UserLoginRequest(BaseModel):
    email: EmailStr
    password: str

class UserProfileUpdate(BaseModel):
    full_name: str = None
    company: str = None

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    email: str
    role: str

def create_access_token(user_id: str, email: str, role: str = "user") -> str:
    """Create JWT access token for user."""
    if not JWT_SECRET:
        raise HTTPException(status_code=500, detail="JWT secret not configured")
    
    payload = {
        "sub": user_id,
        "email": email,
        "role": role,
        "exp": datetime.utcnow() + timedelta(hours=24),
        "iat": datetime.utcnow()
    }
    
    return jwt.encode(payload, JWT_SECRET, algorithm="HS256")

@router.post("/auth/signup", response_model=TokenResponse)
async def signup_user(request: UserSignupRequest):
    """Sign up a new user."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        # Check if user already exists
        existing_user = await supabase_service.get_user_by_email(request.email)
        if existing_user and "error" not in existing_user:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Create new user
        user_id = str(uuid.uuid4())
        
        user_data = await supabase_service.create_user_profile(
            user_id=user_id,
            email=request.email,
            full_name=request.full_name,
            company=request.company,
            role="user",
            subscription_tier="free"
        )
        
        if "error" in user_data:
            raise HTTPException(status_code=400, detail=user_data["error"])
        
        # Create access token
        access_token = create_access_token(user_id, request.email, "user")
        
        return TokenResponse(
            access_token=access_token,
            user_id=user_id,
            email=request.email,
            role="user"
        )
        
    except Exception as e:
        logger.error(f"Signup error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/auth/login", response_model=TokenResponse)
async def login_user(request: UserLoginRequest):
    """Login user."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        # Get user by email
        user_data = await supabase_service.get_user_by_email(request.email)
        
        if "error" in user_data or not user_data:
            raise HTTPException(status_code=401, detail="Invalid email or password")
        
        # In production, verify password here
        # For now, we'll just check if user exists
        
        # Create access token
        access_token = create_access_token(user_data["id"], request.email, user_data.get("role", "user"))
        
        return TokenResponse(
            access_token=access_token,
            user_id=user_data["id"],
            email=request.email,
            role=user_data.get("role", "user")
        )
        
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/auth/me")
async def get_current_user_profile(user = AuthenticatedUser):
    """Get current user profile."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        user_profile = await supabase_service.get_user_profile(user["user_id"])
        
        if "error" in user_profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        return user_profile
        
    except Exception as e:
        logger.error(f"Profile fetch error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/auth/profile")
async def update_user_profile(request: UserProfileUpdate, user = AuthenticatedUser):
    """Update user profile."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        user_profile = await supabase_service.update_user_profile(
            user["user_id"], **update_data
        )
        
        if "error" in user_profile:
            raise HTTPException(status_code=400, detail=user_profile["error"])
        
        return user_profile
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/auth/usage")
async def get_user_usage(user = AuthenticatedUser):
    """Get user usage statistics."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        usage_stats = await supabase_service.get_user_usage_stats(user["user_id"])
        
        if "error" in usage_stats:
            raise HTTPException(status_code=400, detail=usage_stats["error"])
        
        return usage_stats
        
    except Exception as e:
        logger.error(f"Usage stats error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/auth/documents")
async def get_user_documents(user = AuthenticatedUser):
    """Get user's documents."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        documents = await supabase_service.get_user_documents(user["user_id"])
        return {"status": "success", "documents": documents}
        
    except Exception as e:
        logger.error(f"Documents fetch error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/auth/subscription-plans")
async def get_subscription_plans():
    """Get available subscription plans."""
    if not supabase_service.is_available():
        raise HTTPException(status_code=503, detail="Database service unavailable")
    
    try:
        plans = await supabase_service.get_subscription_plans()
        return {"status": "success", "plans": plans}
        
    except Exception as e:
        logger.error(f"Subscription plans error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/auth/token")
async def get_user_from_token(request: Request):
    """Legacy endpoint - get user from token."""
    user = decode_jwt_from_header(request)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid token payload")
    return user
