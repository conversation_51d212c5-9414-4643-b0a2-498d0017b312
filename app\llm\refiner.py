import logging
import async<PERSON>
import re
from langchain_core.prompts import PromptTemplate
from langchain_together import Together
from tenacity import retry, stop_after_attempt, wait_fixed
from app.config import LLM_CONFIG

logger = logging.getLogger(__name__)

# Initialize LLM with centralized config
llm = Together(**LLM_CONFIG)

# Prompt Template
prompt = PromptTemplate.from_template("""
You are an AI assistant designed to refine and complete partially structured project requirement details.

You are given:
1. The raw text of a PRD (Product Requirements Document).
2. The initial extraction result from a parser, which may be incomplete or partially incorrect.

Your task:
- Use the PRD text to cross-check and intelligently fill in missing values or correct inaccuracies in the parsed data.
- Return your answer as **a single clean Markdown block** (not JSON), and avoid repeating the output.
- Expand vague entries with plausible detail based on the PRD.
- Include the following fields in the exact order: title, objective, features (with name, description, priority, dependencies), target users, assumptions, non-goals, success metrics, milestones (with name, description, due date), tech stack suggestions, and notes.
- Do NOT return multiple versions or wrap the output in backticks.

---

PRD TEXT:
{prd_text}

---

INITIAL PARSER OUTPUT:
{parser_output}

---

Refined and completed structured fields (as a clean single markdown block):
""")

chain = prompt | llm


def trim_duplicate_markdown(text: str) -> str:
    """
    Keeps only the first markdown block starting with '### title'
    and removes any trailing explanation or assistant commentary.
    """
    split_blocks = text.strip().split("### title")
    if len(split_blocks) > 1:
        content = "### title" + split_blocks[1].strip()
    else:
        content = text.strip()

    # Remove trailing assistant-style commentary or code blocks
    content = re.split(r"(---\nPlease let me know|```python|```#)", content)[0].strip()
    return content


# Retry wrapper for robustness
async def refine_parsed_output(raw_parser_output: str, prd_text: str) -> str:
    logger.info("🔧 Running refiner on parsed output...")

    attempt = 0
    max_retries = 3
    delay = 2

    while attempt < max_retries:
        try:
            result = await chain.ainvoke(
                {
                    "parser_output": raw_parser_output,
                    "prd_text": prd_text,
                }
            )
            cleaned = trim_duplicate_markdown(result)
            logger.info("Refiner final output:")
            logger.info(cleaned)
            return cleaned
        except Exception as e:
            attempt += 1
            logger.warning(f"Refiner attempt {attempt} failed: {e}")
            if attempt < max_retries:
                await asyncio.sleep(delay)
            else:
                logger.error("Refiner step failed after retries.")
                raise e
