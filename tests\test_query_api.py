import asyncio
import httpx

API_URL = "http://localhost:8000/api/query"

SAMPLE_QUESTION = "What are the main features of this system?"
SAMPLE_QUERY = "authentication system"
SAMPLE_DOCUMENT_ID = None  # Fill this with a real document_id if available

async def main():
    results = []
    async with httpx.AsyncClient(timeout=60.0) as client:
        print("\n--- Testing POST / ---")
        resp = await client.post(API_URL + "/", json={
            "question": SAMPLE_QUESTION,
            "document_id": SAMPLE_DOCUMENT_ID,
            "limit": 3
        })
        data = resp.json()
        ok = data.get("status") == "success" and "answer" in data or "question" in data
        results.append(("/query", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /search-only ---")
        resp = await client.post(API_URL + "/search-only", json={
            "query": SAMPLE_QUERY,
            "limit": 2
        })
        data = resp.json()
        ok = data.get("status") == "success" and "results" in data
        results.append(("/search-only", ok, data))
        await asyncio.sleep(2)

        print("\n--- Testing /documents ---")
        resp = await client.get(API_URL + "/documents")
        docs = resp.json()
        ok = docs.get("status") == "success"
        results.append(("/documents", ok, docs))
        await asyncio.sleep(2)

        doc_id = SAMPLE_DOCUMENT_ID
        doc_list = docs.get("documents") or docs.get("vector_store_documents")
        if not doc_id and doc_list and len(doc_list) > 0:
            doc_id = doc_list[0].get("document_id") or doc_list[0].get("id")

        if doc_id:
            print(f"\n--- Testing /documents/{{document_id}}/chunks ---")
            resp = await client.get(f"{API_URL}/documents/{doc_id}/chunks")
            data = resp.json()
            ok = data.get("status") == "success" and "chunks" in data
            results.append(("/documents/{document_id}/chunks", ok, data))
            await asyncio.sleep(2)

            print(f"\n--- Testing DELETE /documents/{{document_id}} ---")
            resp = await client.delete(f"{API_URL}/documents/{doc_id}")
            data = resp.json()
            ok = data.get("status") == "success"
            results.append(("/documents/{document_id} DELETE", ok, data))
            await asyncio.sleep(2)

        print("\n--- Testing /health ---")
        resp = await client.get(API_URL + "/health")
        data = resp.json()
        ok = "status" in data
        results.append(("/health", ok, data))

    print("\n===== Query API Test Summary =====")
    for route, ok, data in results:
        if ok:
            print(f"[PASS] {route}")
        else:
            print(f"[FAIL] {route} - Response: {data}")
    print("===============================\n")

if __name__ == "__main__":
    asyncio.run(main()) 