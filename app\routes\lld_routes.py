from fastapi import APIRouter, Request
from app.lld.lld_orchestrator import (
    generate_lld_database_schema,
    generate_class_structure,
    generate_lld_dml_statements,
    generate_complete_lld
)
from app.auth.auth_guard import OptionalUser
import logging
import time

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/database-schema")
async def generate_db_schema_route(request: Request):
    """Generate database schema based on PRD and HLD components."""
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        schema = await generate_lld_database_schema(prd_summary, hld_components)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "database_schema": schema,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


@router.post("/class-structure")
async def generate_class_structure_route(request: Request):
    """Generate class structure based on PRD and HLD components."""
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        class_structure = await generate_class_structure(prd_summary, hld_components)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "class_structure": class_structure,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


@router.post("/dml-statements")
async def generate_dml_statements_route(request: Request):
    """Generate DML statements based on PRD and HLD components."""
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        dml_statements = await generate_lld_dml_statements(prd_summary, hld_components)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "dml_statements": dml_statements,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


@router.post("/complete")
async def generate_complete_lld_route(request: Request):
    """Generate complete LLD including database schema, class structure, and DML statements."""
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        complete_lld = await generate_complete_lld(prd_summary, hld_components)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "complete_lld": complete_lld,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)} 