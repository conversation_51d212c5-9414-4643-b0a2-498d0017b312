# 🚀 Render Deployment Guide

## 📋 Prerequisites

Before deploying to Render, ensure you have:

1. **GitHub Repository** - Your code pushed to GitHub
2. **Render Account** - Sign up at [render.com](https://render.com)
3. **Environment Variables** - Your API keys ready

## 🔧 Environment Variables Setup

### **Required Environment Variables**

You'll need to set these in your Render dashboard:

| Variable | Description | Example |
|----------|-------------|---------|
| `TOGETHER_API_KEY` | Your Together AI API key | `your_together_ai_key_here` |
| `SUPABASE_URL` | Your Supabase project URL | `https://your-project.supabase.co` |
| `SUPABASE_KEY` | Your Supabase anon key | `your_supabase_anon_key` |
| `TOGETHER_MODEL` | AI model to use | `meta-llama/Llama-3.3-70B-Instruct-Turbo-Free` |
| `QDRANT_URL` | Vector database URL | `http://localhost:6333` |
| `ENVIRONMENT` | Environment type | `production` |

### **How to Get Your API Keys**

#### **Together AI**
1. Go to [together.ai](https://together.ai)
2. Sign up and create an account
3. Go to API Keys section
4. Copy your API key

#### **Supabase**
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Go to Settings > API
4. Copy your URL and anon key

## 🚀 Deployment Steps

### **Method 1: Using Render Dashboard (Recommended)**

1. **Connect GitHub Repository**
   - Go to [render.com](https://render.com)
   - Click "New +" → "Web Service"
   - Connect your GitHub account
   - Select your repository

2. **Configure Service**
   ```
   Name: autospec-design-backend
   Environment: Python
   Build Command: pip install -r requirements.txt
   Start Command: uvicorn app.main:app --host 0.0.0.0 --port $PORT
   ```

3. **Set Environment Variables**
   - Click "Environment" tab
   - Add all required variables listed above
   - Save changes

4. **Deploy**
   - Click "Create Web Service"
   - Wait for build to complete
   - Your API will be available at the provided URL

### **Method 2: Using render.yaml (Alternative)**

1. **Push render.yaml to GitHub**
   - The file is already created in your repository
   - Render will automatically detect it

2. **Deploy from Dashboard**
   - Go to Render dashboard
   - Click "New +" → "Blueprint"
   - Select your repository
   - Render will use the render.yaml configuration

## 🔍 Post-Deployment Verification

### **1. Health Check**
```bash
curl https://your-app-name.onrender.com/health
```

### **2. API Documentation**
Visit: `https://your-app-name.onrender.com/docs`

### **3. Test Endpoints**
```bash
# Test HLD narrative
curl -X POST "https://your-app-name.onrender.com/api/hld/narrative" \
  -H "Content-Type: application/json" \
  -d '{"clean_text": "Test PRD", "structured_prd": "Test summary"}'
```

## 🛠️ Troubleshooting

### **Common Issues**

#### **1. Build Failures**
- **Issue**: Dependencies not installing
- **Solution**: Check requirements.txt and ensure all packages are compatible

#### **2. Environment Variables**
- **Issue**: API keys not working
- **Solution**: Double-check all environment variables are set correctly

#### **3. Port Issues**
- **Issue**: App not starting
- **Solution**: Ensure start command uses `$PORT` variable

#### **4. Memory Issues**
- **Issue**: App crashes due to memory
- **Solution**: Consider upgrading to a paid plan for more resources

### **Debugging Steps**

1. **Check Logs**
   - Go to your service in Render dashboard
   - Click "Logs" tab
   - Look for error messages

2. **Test Locally**
   ```bash
   # Test with production environment
   ENVIRONMENT=production uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

3. **Verify Environment Variables**
   ```bash
   # Add this to your main.py temporarily
   import os
   print("Environment variables:")
   print(f"TOGETHER_API_KEY: {'SET' if os.getenv('TOGETHER_API_KEY') else 'NOT SET'}")
   print(f"SUPABASE_URL: {'SET' if os.getenv('SUPABASE_URL') else 'NOT SET'}")
   ```

## 📊 Performance Optimization

### **Render Free Tier Limitations**
- **512 MB RAM** - Monitor memory usage
- **750 hours/month** - Free tier limit
- **Sleep after inactivity** - Service may sleep

### **Optimization Tips**

1. **Use Production Requirements**
   ```bash
   # Use requirements-prod.txt for smaller builds
   pip install -r requirements-prod.txt
   ```

2. **Enable Auto-Deploy**
   - Connect GitHub repository
   - Enable auto-deploy on push

3. **Monitor Usage**
   - Check Render dashboard regularly
   - Monitor API response times

## 🔒 Security Considerations

### **Environment Variables**
- ✅ **Never commit API keys** to GitHub
- ✅ **Use Render environment variables**
- ✅ **Rotate keys regularly**

### **CORS Configuration**
```python
# Already configured in main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 📈 Scaling

### **When to Upgrade**

1. **High Traffic** - Upgrade to paid plan
2. **Memory Issues** - Increase RAM allocation
3. **Performance** - Consider dedicated instances

### **Upgrade Steps**
1. Go to your service in Render dashboard
2. Click "Settings" → "Plan"
3. Choose appropriate plan
4. Redeploy if needed

## 🎯 Production Checklist

- ✅ **Environment variables set**
- ✅ **API keys configured**
- ✅ **Database connected**
- ✅ **Health check working**
- ✅ **API docs accessible**
- ✅ **CORS configured**
- ✅ **Error handling working**
- ✅ **Logs monitored**

## 📞 Support

### **Render Support**
- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)

### **Application Support**
- Check logs in Render dashboard
- Test endpoints with curl
- Verify environment variables

---

**Your backend will be live at: `https://your-app-name.onrender.com`** 🚀 