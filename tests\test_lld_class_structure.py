import asyncio
import httpx

API_URL = "http://localhost:8000/api/lld/class-structure"
PRD_SUMMARY = "E-commerce platform with user authentication, product catalog, and payments."
HLD_COMPONENTS = [
    {"name": "User Service", "role": "Handles user registration and authentication"},
    {"name": "Product Catalog", "role": "Manages product listings and inventory"},
    {"name": "Order Service", "role": "Handles order placement and tracking"},
    {"name": "Payment Gateway", "role": "Processes payments securely"}
]

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /lld/class-structure ---")
        resp = await client.post(API_URL, json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        print("/lld/class-structure response:", data)
        ok = data.get("status") == "success" and "class_structure" in data
        if ok:
            print("[PASS] /lld/class-structure - class_structure present.")
        else:
            print(f"[FAIL] /lld/class-structure - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 