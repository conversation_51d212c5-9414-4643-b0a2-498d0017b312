{"data_mtime": 1753995759, "dep_lines": [11, 27, 32, 9, 11, 12, 13, 14, 15, 16, 35, 77, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 10, 10, 5, 10, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "ctypes.wintypes", "click._compat", "__future__", "collections", "io", "sys", "time", "typing", "ctypes", "msvcrt", "typing_extensions", "builtins", "_collections_abc", "_ctypes", "_frozen_importlib", "_io", "abc"], "hash": "f5fdebd0969bde9a4a1dd337fef42b126f9bb1db", "id": "click._winconsole", "ignore_all": true, "interface_hash": "fcade56dbe0c373823b4ac4364ec6a9e02aa4495", "mtime": 1753995713, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\_winconsole.py", "plugin_data": null, "size": 8465, "suppressed": [], "version_id": "1.15.0"}