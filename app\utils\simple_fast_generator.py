#!/usr/bin/env python3
"""
Simple Fast Generator using existing working patterns
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential
from langchain_together import Together

logger = logging.getLogger(__name__)

class SimpleFastGenerator:
    def __init__(self):
        self.ai_client = Together(
            model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            together_api_key=os.getenv("TOGETHER_API_KEY"),
            max_tokens=200
        )
        
    @retry(stop=stop_after_attempt(2), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_hld_narrative_simple(self, prd_text: str) -> Optional[str]:
        """Generate HLD narrative with simple prompt."""
        prompt = f"""
        Based on this PRD, create a brief high-level design narrative (2-3 sentences):
        
        PRD: {prd_text[:500]}
        
        Response (just the narrative, no JSON):
        """
        
        try:
            response = self.ai_client.invoke(prompt)
            return response.strip() if response else None
        except Exception as e:
            logger.error(f"Failed to generate HLD narrative: {e}")
            return None

    @retry(stop=stop_after_attempt(2), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_hld_components_simple(self, prd_text: str) -> List[Dict[str, str]]:
        """Generate HLD components with simple prompt."""
        prompt = f"""
        List 3-5 main components for this system:
        
        PRD: {prd_text[:300]}
        
        Format: name|role
        Example: API Gateway|Routes requests to services
        """
        
        try:
            response = self.ai_client.invoke(prompt)
            if response:
                components = []
                lines = response.strip().split('\n')
                for line in lines:
                    if '|' in line:
                        name, role = line.split('|', 1)
                        components.append({
                            "name": name.strip(),
                            "role": role.strip(),
                            "integration_points": ["API Gateway"],
                            "design_rationale": "Core system component"
                        })
                return components[:5]  # Limit to 5 components
            return []
        except Exception as e:
            logger.error(f"Failed to generate HLD components: {e}")
            return []

    @retry(stop=stop_after_attempt(2), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_database_schema_simple(self, prd_text: str) -> Dict[str, Any]:
        """Generate database schema with simple prompt."""
        prompt = f"""
        Create a simple database schema for this system:
        
        PRD: {prd_text[:300]}
        
        Format: table_name|column1:type,column2:type
        Example: users|id:int,email:string,name:string
        """
        
        try:
            response = self.ai_client.invoke(prompt)
            if response:
                tables = []
                lines = response.strip().split('\n')
                for line in lines:
                    if '|' in line:
                        table_name, columns_str = line.split('|', 1)
                        columns = []
                        for col in columns_str.split(','):
                            if ':' in col:
                                col_name, col_type = col.split(':', 1)
                                columns.append({
                                    "name": col_name.strip(),
                                    "type": col_type.strip(),
                                    "constraints": []
                                })
                        tables.append({
                            "name": table_name.strip(),
                            "columns": columns,
                            "description": f"Table for {table_name.strip()}"
                        })
                
                return {
                    "database_type": "PostgreSQL",
                    "tables": tables[:5]  # Limit to 5 tables
                }
            return {"database_type": "PostgreSQL", "tables": []}
        except Exception as e:
            logger.error(f"Failed to generate database schema: {e}")
            return {"database_type": "PostgreSQL", "tables": []}

    @retry(stop=stop_after_attempt(2), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_class_structure_simple(self, prd_text: str) -> Dict[str, Any]:
        """Generate class structure with simple prompt."""
        prompt = f"""
        Create 2-3 main classes for this system:
        
        PRD: {prd_text[:300]}
        
        Format: class_name|method1,method2
        Example: UserService|authenticate,register,getProfile
        """
        
        try:
            response = self.ai_client.invoke(prompt)
            if response:
                classes = []
                lines = response.strip().split('\n')
                for line in lines:
                    if '|' in line:
                        class_name, methods_str = line.split('|', 1)
                        methods = [m.strip() for m in methods_str.split(',')]
                        classes.append({
                            "name": class_name.strip(),
                            "methods": methods,
                            "description": f"Service for {class_name.strip()}"
                        })
                
                return {
                    "classes": classes[:3]  # Limit to 3 classes
                }
            return {"classes": []}
        except Exception as e:
            logger.error(f"Failed to generate class structure: {e}")
            return {"classes": []}

    def generate_complete_hld_lld_simple(self, prd_text: str) -> Dict[str, Any]:
        """Generate complete HLD and LLD sequentially but fast."""
        logger.info("Starting simple fast HLD/LLD generation...")
        
        try:
            # Generate all components sequentially but with simple prompts
            narrative = self.generate_hld_narrative_simple(prd_text)
            components = self.generate_hld_components_simple(prd_text)
            db_schema = self.generate_database_schema_simple(prd_text)
            class_structure = self.generate_class_structure_simple(prd_text)
            
            return {
                "status": "success",
                "hld": {
                    "narrative": narrative or "High-level design for the system",
                    "components": components
                },
                "lld": {
                    "database_schema": db_schema,
                    "class_structure": class_structure
                },
                "generation_time": "simple_fast"
            }
            
        except Exception as e:
            logger.error(f"Failed to generate complete HLD/LLD: {e}")
            return {
                "status": "error",
 