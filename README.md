# AutoSpec Design - Backend API

A comprehensive backend API for generating system designs from PRD (Product Requirements Document) using AI and RAG (Retrieval-Augmented Generation).

## 🏗️ Architecture

This is a **backend-only repository** that provides RESTful APIs for:
- **HLD (High-Level Design)** generation
- **LLD (Low-Level Design)** generation  
- **Document processing** and analysis
- **AI-powered** system design recommendations

## 🚀 Features

### HLD Generation
- **Narrative Generation** - Comprehensive system overview
- **Component Analysis** - System components and responsibilities
- **Sequence Diagrams** - Mermaid.js sequence diagrams
- **Tech Stack Recommendations** - Technology stack suggestions
- **Assumptions & Constraints** - System assumptions and risks
- **Issues & Challenges** - Potential problems and solutions

### LLD Generation
- **Class Structure** - Object-oriented design patterns
- **Complete LLD** - Full low-level design package

### Document Processing
- **PRD Upload** - Document upload and processing
- **Text Extraction** - Clean text extraction from documents
- **Structured Analysis** - PRD parsing and structuring

## 🛠️ Tech Stack

- **Framework**: FastAPI (Python)
- **AI/LLM**: Together AI (Llama-3.3-70B-Instruct-Turbo-Free)
- **Vector Database**: Qdrant
- **Authentication**: JWT (planned)
- **Document Processing**: PyPDF2, python-docx
- **Async Processing**: asyncio, tenacity

## 📁 Project Structure

```
AutoSpecDesign/
├── app/
│   ├── hld/                    # High-Level Design modules
│   │   ├── hld_narrative.py   # Narrative generation
│   │   ├── hld_components.py  # Component analysis
│   │   ├── hld_sequence.py    # Sequence diagrams
│   │   ├── hld_tech_stack.py  # Tech stack recommendations
│   │   ├── hld_assumptions.py # Assumptions & constraints
│   │   └── hld_issues.py      # Issues & challenges
│   ├── lld/                    # Low-Level Design modules
│   │   ├── class_structure_generator.py # Class structure
│   │   └── lld_orchestrator.py       # Main coordinator
│   ├── routes/                 # API routes
│   │   ├── hld_routes.py      # HLD endpoints
│   │   └── upload.py          # Document upload
│   ├── services/               # Business logic
│   ├── utils/                  # Utilities
│   └── main.py                # FastAPI application
├── requirements.txt            # Python dependencies
└── README.md                  # This file
```

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Clone the repository
git clone <your-backend-repo-url>
cd AutoSpecDesign

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp env.example .env

# Edit .env with your credentials
# TOGETHER_API_KEY=your_together_ai_key
```

### 2. Start the Server
```bash
# Development server
uvicorn app.main:app --reload

# Production server
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 📚 API Documentation

Once the server is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc

## 🔌 API Endpoints

### HLD Endpoints
- `POST /api/hld/narrative` - Generate HLD narrative
- `POST /api/hld/components` - Generate system components
- `POST /api/hld/sequence` - Generate sequence diagrams
- `POST /api/hld/tech-stack` - Generate tech stack recommendations
- `POST /api/hld/assumptions` - Generate assumptions & constraints
- `POST /api/hld/issues` - Generate issues & challenges

### LLD Endpoints
- `POST /api/lld/complete` - Generate complete LLD

### Document Processing
- `POST /api/upload/` - Upload and process documents

## 🔗 Frontend Integration

This backend is designed to work with any frontend framework:
- **React/Next.js** (recommended)
- **Vue.js**
- **Angular**
- **Vanilla JavaScript**

### CORS Configuration
The API includes CORS middleware for frontend integration:
```python
# Already configured in main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 🧪 Testing

Test the API endpoints:
```bash
# Test HLD generation
curl -X POST "http://localhost:8000/api/hld/narrative" \
  -H "Content-Type: application/json" \
  -d '{"clean_text": "Your PRD text", "structured_prd": "Summary"}'

# Test LLD generation
curl -X POST "http://localhost:8000/api/lld/complete" \
  -H "Content-Type: application/json" \
  -d '{"prd_summary": "Summary", "hld_components": []}'
```

## 🔧 Development

### Adding New Features
1. Create new modules in `app/hld/` or `app/lld/`
2. Add routes in `app/routes/`
3. Update `app/main.py` if needed
4. Test with the interactive API docs

### Environment Variables
```bash
# Required
TOGETHER_API_KEY=your_together_ai_key
TOGETHER_MODEL=meta-llama/Llama-3.3-70B-Instruct-Turbo-Free

# Vector Database (Qdrant)
QDRANT_URL=http://localhost:6333
```

## 📈 Performance

- **Async Processing**: All AI operations are asynchronous
- **Rate Limiting**: Respects Together AI rate limits (6 queries/minute)
- **Fallback Mechanisms**: Graceful degradation when AI fails
- **Caching**: Ready for Redis integration

## 🔒 Security

- **Input Validation**: All endpoints validate input
- **Error Handling**: Comprehensive error handling
- **Rate Limiting**: Built-in rate limiting (planned)
- **Authentication**: JWT authentication (planned)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the code structure and examples

---

**Ready for frontend integration!** 🎨✨