import logging
import json
import async<PERSON>
from typing import Dict, List, Any
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re, json

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1024,
    together_api_key=TOGETHER_API_KEY,
)

# Tech Stack Generation Prompt
TECH_STACK_PROMPT = """
You are an expert technology architect. Based on the PRD and HLD components, recommend the most suitable technology stack.

Requirements:
1. Recommend frontend technologies (React, Vue, Angular, etc.)
2. Recommend backend technologies (Node.js, Python, Java, etc.)
3. Recommend database technologies (PostgreSQL, MongoDB, Redis, etc.)
4. Recommend infrastructure and deployment (AWS, Docker, Kubernetes, etc.)
5. Consider scalability, maintainability, and team expertise
- Return ONLY a valid JSON object, no explanations, no markdown, no code blocks, no step-by-step, no final answer, no boxed answer, no extra text. If you cannot answer, return an empty object {}.

Example:
{
  "frontend": {
    "framework": "React",
    "libraries": ["React Router", "Axios"],
    "styling": "Tailwind CSS",
    "state_management": "Context API"
  },
  "backend": {
    "language": "Node.js",
    "framework": "Express",
    "libraries": ["JWT", "bcrypt"],
    "api_design": "REST"
  },
  "database": {
    "primary": "PostgreSQL",
    "caching": "Redis",
    "search": "Elasticsearch"
  },
  "infrastructure": {
    "hosting": "AWS",
    "containerization": "Docker",
    "ci_cd": "GitHub Actions",
    "monitoring": "Prometheus"
  },
  "reasoning": "Modern, scalable stack with good community support"
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

def extract_json_object_strict(llm_response: str):
    try:
        cleaned = llm_response.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)  # Remove headings
        cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        match = re.search(r"\{.*\}", cleaned, re.DOTALL)
        if not match:
            logger.error(f"No JSON object found in LLM output. Raw response: {llm_response}")
            return {}
        json_str = match.group(0)
        try:
            parsed = json.loads(json_str)
            return parsed if isinstance(parsed, dict) else {}
        except Exception as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"JSON string: {json_str}")
            logger.error(f"Raw LLM response: {llm_response}")
            return {}
    except Exception as e:
        logger.error(f"Error during JSON object extraction: {str(e)}")
        logger.error(f"Raw LLM response: {llm_response}")
        return {}

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_tech_stack(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    """Generate technology stack recommendations."""
    logger.info("Generating tech stack recommendations...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = TECH_STACK_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        response = await asyncio.get_event_loop().run_in_executor(None, llm.invoke, prompt)
        logger.info(f"Raw LLM output for tech stack: {response}")
        tech_stack = extract_json_object_strict(response)
        logger.info("Tech stack generated successfully")
        return tech_stack
    except Exception as e:
        logger.error(f"Failed to generate tech stack: {str(e)}")
        return {}

def get_fallback_tech_stack() -> Dict[str, Any]:
    """Get fallback tech stack when generation fails."""
    return {
        "frontend": {
            "framework": "React",
            "libraries": ["React Router", "Axios"],
            "styling": "Tailwind CSS",
            "state_management": "Context API"
        },
        "backend": {
            "language": "Node.js",
            "framework": "Express",
            "libraries": ["JWT", "bcrypt"],
            "api_design": "REST"
        },
        "database": {
            "primary": "PostgreSQL",
            "caching": "Redis",
            "search": "Elasticsearch"
        },
        "infrastructure": {
            "hosting": "AWS",
            "containerization": "Docker",
            "ci_cd": "GitHub Actions",
            "monitoring": "Prometheus"
        },
        "reasoning": "Modern, scalable stack with good community support"
    } 