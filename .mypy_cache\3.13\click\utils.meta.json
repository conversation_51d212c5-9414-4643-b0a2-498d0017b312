{"data_mtime": 1753995761, "dep_lines": [3, 12, 22, 163, 1, 3, 4, 5, 6, 7, 8, 9, 25, 514, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 20, 10, 10, 10, 10, 5, 5, 25, 20, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "click._compat", "click.globals", "click.exceptions", "__future__", "collections", "os", "re", "sys", "typing", "functools", "types", "typing_extensions", "errno", "builtins", "_frozen_importlib", "_io", "abc", "io"], "hash": "67cd3dccfdeff6bd19589a3831bbc94730b771d3", "id": "click.utils", "ignore_all": true, "interface_hash": "abf839d565a73820acbaeb93f8187e122ddb58a9", "mtime": 1753995713, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\utils.py", "plugin_data": null, "size": 20245, "suppressed": [], "version_id": "1.15.0"}