import logging
import json
import async<PERSON>
from typing import Dict, List, Any
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=2048,
    together_api_key=TOGETHER_API_KEY,
)

# More forgiving prompt
CLASS_STRUCTURE_PROMPT = """
You are an expert software architect. Based on the PRD and HLD components, design comprehensive class structures for the system.

Requirements:
1. Design classes for each major component (at least 3-4). If unsure, make your best guess. If you cannot find enough, invent plausible classes for a typical system.
2. Include relationships and inheritance.
3. Add methods and properties.
4. Consider design patterns (Repository, Factory, etc.).
5. Include error handling and validation.
- Do not include explanations, markdown, or code blocks. Just return a JSON object as shown in the example.

Example:
{
  "classes": [
    {
      "name": "UserService",
      "description": "Handles user-related operations",
      "attributes": [
        {"name": "db_connection", "type": "DatabaseConnection", "access": "private", "description": "Database connection"}
      ],
      "methods": [
        {"name": "create_user", "parameters": [{"name": "email", "type": "str", "description": "User email"}], "return_type": "User", "description": "Creates a new user"}
      ],
      "relationships": [
        {"type": "association", "target": "UserRepository"}
      ]
    }
  ],
  "design_patterns": [
    {"pattern": "Repository", "description": "Data access abstraction", "classes_involved": ["UserService", "UserRepository"]}
  ]
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

CLASS_STRUCTURE_FALLBACK = {
    "classes": [
        {
            "name": "UserService",
            "description": "Handles user-related operations",
            "attributes": [
                {"name": "db_connection", "type": "DatabaseConnection", "access": "private", "description": "Database connection"}
            ],
            "methods": [
                {"name": "create_user", "parameters": [{"name": "email", "type": "str", "description": "User email"}], "return_type": "User", "description": "Creates a new user"}
            ],
            "relationships": [
                {"type": "association", "target": "UserRepository"}
            ]
        }
    ],
    "design_patterns": [
        {"pattern": "Repository", "description": "Data access abstraction", "classes_involved": ["UserService", "UserRepository"]}
    ]
}

def extract_first_json_object(text: str, required_keys=None, fallback=None):
    try:
        cleaned = text.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)
        cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        match = re.search(r"\{.*?\}", cleaned, re.DOTALL)
        if not match:
            logger.error(f"No JSON object found in LLM output. Raw response: {text}")
            return fallback if fallback is not None else {}
        json_str = match.group(0)
        try:
            parsed = json.loads(json_str)
            if required_keys:
                missing = [k for k in required_keys if k not in parsed]
                if missing:
                    logger.error(f"Missing keys: {missing}")
                    return fallback if fallback is not None else {}
            return parsed
        except Exception as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"JSON string: {json_str}")
            logger.error(f"Raw LLM response: {text}")
            return fallback if fallback is not None else {}
    except Exception as e:
        logger.error(f"Error during JSON object extraction: {str(e)}")
        logger.error(f"Raw LLM response: {text}")
        return fallback if fallback is not None else {}

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_class_structure(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    """Generate class structure based on PRD and HLD components (fresh)."""
    logger.info("Generating class structure (fresh)...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = CLASS_STRUCTURE_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        response = await asyncio.get_event_loop().run_in_executor(None, llm.invoke, prompt)
        logger.info(f"Raw LLM output for class structure (fresh): {response}")
        class_structure = extract_first_json_object(response, required_keys=["classes", "design_patterns"], fallback=CLASS_STRUCTURE_FALLBACK)
        logger.info("Class structure generated successfully (fresh)")
        return class_structure
    except Exception as e:
        logger.error(f"Failed to generate class structure (fresh): {str(e)}")
        return CLASS_STRUCTURE_FALLBACK

def get_fallback_class_structure() -> Dict[str, Any]:
    """Get a fallback class structure when generation fails (fresh)."""
    return CLASS_STRUCTURE_FALLBACK 