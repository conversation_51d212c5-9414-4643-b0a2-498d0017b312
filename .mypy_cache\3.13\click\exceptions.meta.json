{"data_mtime": 1753995761, "dep_lines": [3, 8, 9, 10, 14, 1, 3, 4, 5, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 5, 20, 10, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "click._compat", "click.globals", "click.utils", "click.core", "__future__", "collections", "typing", "gettext", "builtins", "_frozen_importlib", "abc", "os"], "hash": "20f80cfce63a7d051bf7decdc80e205fbec9748f", "id": "click.exceptions", "ignore_all": true, "interface_hash": "98fe7f8f01f804b4dfcb112de2f9ad820cd7c4bc", "mtime": 1753995713, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\exceptions.py", "plugin_data": null, "size": 9891, "suppressed": [], "version_id": "1.15.0"}