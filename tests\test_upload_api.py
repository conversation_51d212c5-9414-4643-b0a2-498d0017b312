import asyncio
import httpx
import os

API_URL = "http://localhost:8000/api/upload"
SAMPLE_FILE = "sample_prd.txt"

# Create a sample PRD file if not exists
if not os.path.exists(SAMPLE_FILE):
    with open(SAMPLE_FILE, "w") as f:
        f.write("""Product Requirements Document: E-commerce Platform\n\nOverview: ...\n""")

async def main():
    results = []
    async with httpx.AsyncClient(timeout=60.0) as client:
        print("\n--- Testing file upload ---")
        with open(SAMPLE_FILE, "rb") as f:
            resp = await client.post(
                API_URL + "/",
                files={"file": (SAMPLE_FILE, f, "text/plain")},
                data={"title": "Sample PRD"}
            )
        data = resp.json()
        ok = data.get("status") == "success" and "document_id" in data
        results.append(("/upload", ok, data))
        document_id = data.get("document_id")
        await asyncio.sleep(2)

        print("\n--- Testing /search ---")
        resp = await client.post(API_URL + "/search", params={"query": "authentication"})
        data = resp.json()
        ok = data.get("status") == "success" and "results" in data
        results.append(("/search", ok, data))
        await asyncio.sleep(2)

        if document_id:
            print(f"\n--- Testing /download/{{document_id}} ---")
            resp = await client.get(f"{API_URL}/download/{document_id}")
            data = resp.json()
            ok = data.get("status") == "success" and "file_content" in data
            results.append(("/download", ok, data))
            await asyncio.sleep(2)

            print(f"\n--- Testing DELETE /{{document_id}} ---")
            resp = await client.delete(f"{API_URL}/{document_id}")
            data = resp.json()
            ok = data.get("status") == "success"
            results.append(("/delete", ok, data))

    print("\n===== Upload API Test Summary =====")
    for route, ok, data in results:
        if ok:
            print(f"[PASS] {route}")
        else:
            print(f"[FAIL] {route} - Response: {data}")
    print("===============================\n")

if __name__ == "__main__":
    asyncio.run(main()) 