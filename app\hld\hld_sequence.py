#!/usr/bin/env python3
"""
HLD Sequence Diagram Generator
Generates sequence diagrams for system interactions
"""

import logging
import json
import re
from langchain_together import Together
from tenacity import retry, stop_after_attempt, wait_exponential
import os

logger = logging.getLogger(__name__)

class HLDSequenceGenerator:
    def __init__(self):
        self.ai_client = Together(
            model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
            together_api_key=os.getenv("TOGETHER_API_KEY")
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_sequence_diagram(self, structured_prd: str, hld_narrative: str) -> str:
        """Generate a sequence diagram for the system."""
        logger.info("Generating HLD Sequence Diagram...")
        
        prompt = f"""
        Create a sequence diagram for this system. Focus on the main user interactions and system components.

        PRD: {structured_prd}
        HLD Narrative: {hld_narrative}

        Generate ONLY a Mermaid sequence diagram. The response should start with "sequenceDiagram" and show the interactions between:
        - User
        - Frontend (React/Web)
        - Backend (API/Server)
        - Database
        - Any other relevant services

        Format the response as a clean sequence diagram showing authentication and main operations.
        Do not include any explanations, just the sequence diagram code.

        Example format:
        sequenceDiagram
            participant U as User
            participant F as Frontend
            participant B as Backend
            participant D as Database
            U->>F: Login Request
            F->>B: Send Credentials
            B->>D: Validate User
            D->>B: Return User Data
            B->>F: Return Token
            F->>U: Show Dashboard
        """
        
        try:
            response = self.ai_client.invoke(prompt)
            logger.info("Sequence diagram generated successfully.")
            
            # Extract the sequence diagram from the response
            sequence_diagram = self._extract_sequence_diagram(response)
            
            if sequence_diagram:
                return sequence_diagram
            else:
                # Fallback sequence diagram
                return self._get_fallback_sequence_diagram()
                
        except Exception as e:
            logger.error(f"Failed to generate sequence diagram: {e}")
            return self._get_fallback_sequence_diagram()
    
    def _extract_sequence_diagram(self, response: str) -> str:
        """Extract sequence diagram from AI response."""
        if not response:
            return None
        
        # Look for sequenceDiagram in the response
        sequence_pattern = r'sequenceDiagram[\s\S]*?(?=\n\n|\n[A-Z]|$)'
        match = re.search(sequence_pattern, response, re.IGNORECASE)
        
        if match:
            sequence_diagram = match.group(0).strip()
            # Clean up any extra text
            lines = sequence_diagram.split('\n')
            cleaned_lines = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('sequenceDiagram') or line.startswith('participant') or line.startswith('->>') or line.startswith('Note'):
                    cleaned_lines.append(line)
            
            return '\n'.join(cleaned_lines)
        
        return None
    
    def _get_fallback_sequence_diagram(self) -> str:
        """Return a fallback sequence diagram."""
        return """sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant D as Database
    
    U->>F: Login Request
    F->>B: Send Credentials
    B->>D: Validate User
    D->>B: Return User Data
    B->>F: Return Token
    F->>U: Show Dashboard
    
    U->>F: Create Todo
    F->>B: Send Todo Data
    B->>D: Store Todo
    D->>B: Confirm Storage
    B->>F: Return Success
    F->>U: Show Updated List"""

# Global instance
sequence_generator = HLDSequenceGenerator()

async def generate_sequence_diagram(structured_prd: str, hld_narrative: str) -> str:
    """Generate sequence diagram for HLD."""
    return await sequence_generator.generate_sequence_diagram(structured_prd, hld_narrative) 