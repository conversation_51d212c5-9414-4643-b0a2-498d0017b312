import logging
import os
import traceback
from typing import Optional
from langchain_together import Together
from langchain.prompts import PromptTemplate
from tenacity import retry, stop_after_attempt, wait_fixed
from app.config import LLM_CONFIG

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# LLM instantiate with centralized config
llm = Together(**LLM_CONFIG)

# Prompt to llm
prompt = PromptTemplate.from_template(
    """
You are an expert technical product analyst.

Read the PRD text (prd_text) and extract the following fields.

Respond in a plaintext format like this:
Title: ...
Objective: ...
Features:
- Name: ...
  Description: ...
  Priority: ...
  Dependencies: ...
Target Users: ...
Assumptions: ...
Non Goals: ...
Success Metrics: ...
Milestones:
- Name: ...
  Description: ...
  Due Date: ...
Tech Stack Suggestions: ...
Notes: ...

Rules:
- Do not repeat the output block.
- Do not include empty or placeholder fields.
- Do not wrap your output in markdown or code blocks.
- Only respond with a single clean plaintext output — no commentary or instructions.

PRD:
\"\"\"{prd_text}\"\"\"
"""
)


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def query_llm(prompt_input: str) -> str:
    return llm.invoke(prompt.format(prd_text=prompt_input))


def deduplicate_blocks(text: str) -> str:
    """Remove repeated paragraph blocks (not just lines)."""
    blocks = [block.strip() for block in text.strip().split("\n\n") if block.strip()]
    seen = set()
    result = []
    for block in blocks:
        if block not in seen:
            result.append(block)
            seen.add(block)
    return "\n\n".join(result)


def extract_fields_from_text(text: str) -> dict:
    result = {
        "title": None,
        "objective": None,
        "features": [],
        "target_users": None,
        "assumptions": None,
        "non_goals": None,
        "success_metrics": None,
        "milestones": [],
        "tech_stack_suggestions": None,
        "notes": None,
    }

    try:
        lines = text.strip().splitlines()
        current_section = None
        feature = {}
        milestone = {}

        for line in lines:
            line = line.strip()

            if line.startswith("Title:"):
                result["title"] = line.split(":", 1)[1].strip()
            elif line.startswith("Objective:"):
                result["objective"] = line.split(":", 1)[1].strip()
            elif line.startswith("Target Users:"):
                result["target_users"] = line.split(":", 1)[1].strip()
            elif line.startswith("Assumptions:"):
                result["assumptions"] = line.split(":", 1)[1].strip()
            elif line.startswith("Non Goals:"):
                result["non_goals"] = line.split(":", 1)[1].strip()
            elif line.startswith("Success Metrics:"):
                result["success_metrics"] = line.split(":", 1)[1].strip()
            elif line.startswith("Tech Stack Suggestions:"):
                result["tech_stack_suggestions"] = line.split(":", 1)[1].strip()
            elif line.startswith("Notes:"):
                result["notes"] = line.split(":", 1)[1].strip()
            elif line.startswith("Features:"):
                current_section = "features"
            elif line.startswith("Milestones:"):
                current_section = "milestones"
            elif line.startswith("- Name:"):
                name = line.split(":", 1)[1].strip()
                if current_section == "features":
                    feature = {"name": name}
                else:
                    milestone = {"name": name}
            elif line.startswith("Description:"):
                desc = line.split(":", 1)[1].strip()
                if current_section == "features":
                    feature["description"] = desc
                else:
                    milestone["description"] = desc
            elif line.startswith("Priority:"):
                feature["priority"] = line.split(":", 1)[1].strip()
            elif line.startswith("Dependencies:"):
                feature["dependencies"] = line.split(":", 1)[1].strip()
                result["features"].append(feature)
                feature = {}
            elif line.startswith("Due Date:"):
                milestone["due_date"] = line.split(":", 1)[1].strip()
                result["milestones"].append(milestone)
                milestone = {}

    except Exception:
        logger.error("Parsing failed:\n" + traceback.format_exc())

    return result


async def parse_prd_content(prd_text: str) -> Optional[dict]:
    logger.info("Querying LLM for PRD parsing...")
    try:
        raw_response = query_llm(prd_text)
        logger.info("Raw LLM Response:\n" + raw_response)

        cleaned_response = deduplicate_blocks(raw_response)
        parsed = extract_fields_from_text(cleaned_response)

        return parsed
    except Exception as e:
        logger.error("LLM parsing failed.")
        logger.error(str(e))
        logger.error(traceback.format_exc())
        return None
