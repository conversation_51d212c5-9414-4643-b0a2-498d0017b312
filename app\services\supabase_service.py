"""
Supabase Service

Handles all Supabase database operations including user management,
document storage, and API usage logging.
"""
import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class SupabaseService:
    """
    Service class for handling Supabase operations.
    
    This service provides a fallback mechanism when Supabase is not available,
    allowing the application to run in stateless mode.
    """
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        self._client = None
        self._available = False
        
        # Initialize Supabase client if credentials are available
        if self.supabase_url and self.supabase_key:
            try:
                from supabase import create_client, Client
                self._client: Client = create_client(self.supabase_url, self.supabase_key)
                self._available = True
                logger.info("Supabase client initialized successfully")
            except ImportError:
                logger.warning("Supabase library not available. Running in stateless mode.")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
        else:
            logger.info("Supabase credentials not provided. Running in stateless mode.")
    
    def is_available(self) -> bool:
        """Check if Supabase is available and configured."""
        return self._available and self._client is not None
    
    def health_status(self) -> Dict[str, Any]:
        """Get health status of Supabase connection."""
        if not self.is_available():
            return {
                "status": "unavailable",
                "message": "Supabase not configured or client not initialized",
                "stateless_mode": True
            }
        
        try:
            # Simple health check - try to access a table
            response = self._client.table("users").select("count", count="exact").execute()
            return {
                "status": "healthy",
                "message": "Supabase connection successful",
                "stateless_mode": False,
                "user_count": response.count if hasattr(response, 'count') else 0
            }
        except Exception as e:
            logger.error(f"Supabase health check failed: {e}")
            return {
                "status": "error",
                "message": f"Supabase connection failed: {str(e)}",
                "stateless_mode": True
            }
    
    async def get_user_by_email(self, email: str) -> Dict[str, Any]:
        """Get user by email address."""
        if not self.is_available():
            return {"error": "Supabase not available"}
        
        try:
            response = self._client.table("users").select("*").eq("email", email).execute()
            if response.data:
                return response.data[0]
            return {"error": "User not found"}
        except Exception as e:
            logger.error(f"Failed to get user by email: {e}")
            return {"error": str(e)}
    
    async def create_user_profile(
        self, 
        user_id: str, 
        email: str, 
        full_name: str = None,
        company: str = None,
        role: str = "user",
        subscription_tier: str = "free"
    ) -> Dict[str, Any]:
        """Create a new user profile."""
        if not self.is_available():
            return {"error": "Supabase not available"}
        
        try:
            user_data = {
                "user_id": user_id,
                "email": email,
                "full_name": full_name,
                "company": company,
                "role": role,
                "subscription_tier": subscription_tier,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            response = self._client.table("users").insert(user_data).execute()
            if response.data:
                return response.data[0]
            return {"error": "Failed to create user"}
        except Exception as e:
            logger.error(f"Failed to create user profile: {e}")
            return {"error": str(e)}
    
    async def get_user_documents(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all documents for a user."""
        if not self.is_available():
            return []
        
        try:
            response = self._client.table("documents").select("*").eq("user_id", user_id).execute()
            return response.data if response.data else []
        except Exception as e:
            logger.error(f"Failed to get user documents: {e}")
            return []
    
    async def log_api_usage(
        self, 
        user_id: str, 
        endpoint: str, 
        processing_time_ms: int,
        status: str = "success"
    ) -> bool:
        """Log API usage for analytics."""
        if not self.is_available():
            return False
        
        try:
            usage_data = {
                "user_id": user_id,
                "endpoint": endpoint,
                "processing_time_ms": processing_time_ms,
                "status": status,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            response = self._client.table("api_usage").insert(usage_data).execute()
            return bool(response.data)
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")
            return False
    
    async def store_document(
        self,
        document_id: str,
        user_id: str,
        title: str,
        filename: str,
        content: str,
        file_size: int
    ) -> Dict[str, Any]:
        """Store document metadata in database."""
        if not self.is_available():
            return {"error": "Supabase not available"}
        
        try:
            document_data = {
                "document_id": document_id,
                "user_id": user_id,
                "title": title,
                "filename": filename,
                "content": content,
                "file_size": file_size,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            response = self._client.table("documents").insert(document_data).execute()
            if response.data:
                return response.data[0]
            return {"error": "Failed to store document"}
        except Exception as e:
            logger.error(f"Failed to store document: {e}")
            return {"error": str(e)}
    
    async def get_document(self, document_id: str) -> Dict[str, Any]:
        """Get document by ID."""
        if not self.is_available():
            return {"error": "Supabase not available"}
        
        try:
            response = self._client.table("documents").select("*").eq("document_id", document_id).execute()
            if response.data:
                return response.data[0]
            return {"error": "Document not found"}
        except Exception as e:
            logger.error(f"Failed to get document: {e}")
            return {"error": str(e)}

# Global instance
supabase_service = SupabaseService()
