import logging
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re

logger = logging.getLogger(__name__)

# Initialise LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1500,
    together_api_key=TOGETHER_API_KEY,
)

# Detailed and Controlled Prompt
HLD_NARRATIVE_PROMPT = """
You are a system design assistant tasked with writing a brief and clear HLD narrative for a proposed software system.

You are given:
1. The raw PRD text.
2. A structured summary of the PRD fields.

Write a concise high-level design narrative (1-2 paragraphs max) that:
- Summarizes the purpose of the system.
- Highlights key components and their role.
- Briefly explains the technical direction and performance/security expectations.
- Do NOT repeat any phrases or expand unnecessarily.
- Do NOT include any explanations, step-by-step, headings, markdown, code blocks, final answer, or boxed answer. Return ONLY a single plain text paragraph and nothing else.
- If you cannot answer, return an empty string.

Example:
The proposed e-commerce platform is designed to provide users with a seamless online shopping experience, allowing them to browse products, manage their accounts, and securely process transactions. Key components include a user authentication module for secure login and account management, a product catalog for showcasing available items, and a payment gateway for processing transactions. The technical direction involves leveraging a microservices architecture to ensure scalability and flexibility, with a focus on implementing robust security measures to protect user data and ensure compliance with industry standards, while also optimizing for high performance to support a large volume of concurrent users.

PRD TEXT:
{clean_text}

STRUCTURED FIELDS:
{structured_prd}

HLD Narrative:
"""

def extract_first_paragraph(text: str) -> str:
    # Remove markdown/code blocks and explanations
    cleaned = re.sub(r"```[\s\S]*?```", "", text)
    cleaned = re.sub(r"#+.*", "", cleaned)  # Remove headings
    cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
    # Get the first non-empty paragraph
    paragraphs = [p.strip() for p in cleaned.split("\n\n") if p.strip()]
    return paragraphs[0] if paragraphs else ""

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_hld_narrative(structured_prd: str, clean_text: str):
    logger.info("Generating HLD Narrative...")
    try:
        result = await llm.ainvoke(
            HLD_NARRATIVE_PROMPT.format(
                structured_prd=structured_prd, clean_text=clean_text
            )
        )
        logger.info("HLD Narrative generated successfully.")
        return extract_first_paragraph(result.strip())
    except Exception as e:
        logger.error(f"Failed to generate HLD Narrative: {str(e)}")
        raise Exception("Failed to generate HLD Narrative.") from e
