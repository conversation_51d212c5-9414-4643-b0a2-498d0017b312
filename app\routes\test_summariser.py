from fastapi import APIRouter, HTTPException, Request
from app.llm.summariser import summarise_prd

router = APIRouter()


@router.post("/test/summariser")
async def test_summariser(request: Request):
    try:
        body = await request.json()
        prd_text = body.get("prd_text")

        if not prd_text:
            raise HTTPException(
                status_code=400, detail="Missing 'prd_text' in request body."
            )

        summary = await summarise_prd(prd_text)
        return {"status": "success", "summary": summary}

    except HTTPException as http_error:
        return {"status": "error", "message": http_error.detail}

    except Exception as e:
        return {"status": "error", "message": str(e)}
