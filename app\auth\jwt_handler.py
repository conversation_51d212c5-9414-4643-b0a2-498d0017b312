import logging
from fastapi import Request
import jwt
from jwt import ExpiredSignatureError, InvalidTokenError
from app.config import JWT_SECRET

logger = logging.getLogger(__name__)


def decode_jwt_from_header(request: Request):
    """
    Decodes the JWT token from the Authorization header.
    Returns the user_id, email, and role if valid, otherwise returns 'missing'.
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return {"user_id": "missing", "email": "missing", "role": "missing"}

    token = auth_header.split(" ")[1]

    try:
        decoded = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return {
            "user_id": decoded.get("sub", "missing"),
            "email": decoded.get("email", "missing"),
            "role": decoded.get("role", "missing"),
        }
    except ExpiredSignatureError:
        logger.error("JWT decoding error: Signature has expired")
        return {"user_id": "expired", "email": "expired", "role": "expired"}
    except InvalidTokenError:
        logger.error("JWT decoding error: Invalid token")
        return {"user_id": "missing", "email": "missing", "role": "missing"}
    except Exception as e:
        logger.error(f"JWT decoding error: {e}")
        return {"user_id": "missing", "email": "missing", "role": "missing"}
