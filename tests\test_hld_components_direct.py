import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld/components"

STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, shopping cart, admin dashboard, and payments."
HLD_NARRATIVE = (
    "The proposed e-commerce platform enables users to browse products, manage their accounts, and securely complete purchases online. "
    "Key components include a user authentication system for secure access, a product catalog for managing and displaying items, "
    "a shopping cart and checkout process for order management, and an admin dashboard for analytics and content management. "
    "The system is designed for scalability, security, and high availability, leveraging modern cloud infrastructure and best practices."
)

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /hld/components (direct) ---")
        resp = await client.post(API_URL, json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": HLD_NARRATIVE
        })
        data = resp.json()
        print("/hld/components response:", data)
        ok = data.get("status") == "success" and isinstance(data.get("hld_components"), list)
        if ok:
            print("[PASS] /hld/components - hld_components present.")
        else:
            print(f"[FAIL] /hld/components - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 