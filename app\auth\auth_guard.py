from fastapi import Header, HTTPException, Depends
from app.services.supabase_service import supabase_service
import logging

logger = logging.getLogger(__name__)

def verify_token(token: str):
    """Verify JWT token and return payload."""
    try:
        import jwt
        from app.config import JWT_SECRET
        
        if not JWT_SECRET:
            raise HTTPException(status_code=500, detail="JWT secret not configured")
            
        payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(status_code=401, detail="Token verification failed")

async def get_current_user(authorization: str = Header(...)):
    """Extract and verify user from Authorization header."""
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=401, detail="Invalid Authorization header format"
        )

    token = authorization.split(" ")[1]
    payload = verify_token(token)

    if not payload or "sub" not in payload:
        raise HTTPException(status_code=401, detail="Invalid or expired token")

    user_id = payload.get("sub")
    email = payload.get("email")
    role = payload.get("role")

    # Optionally verify user exists in database
    if supabase_service.is_available():
        try:
            user_profile = await supabase_service.get_user_profile(user_id)
            if "error" in user_profile:
                logger.warning(f"User not found in database: {user_id}")
        except Exception as e:
            logger.warning(f"Failed to fetch user profile: {e}")

    return {
        "user_id": user_id,
        "email": email,
        "role": role,
    }

async def get_optional_user(authorization: str = Header(None)):
    """Get user if token is provided, otherwise return None."""
    if not authorization:
        return None
    
    try:
        return await get_current_user(authorization)
    except HTTPException:
        return None

# Dependency for authenticated routes
AuthenticatedUser = Depends(get_current_user)
OptionalUser = Depends(get_optional_user)
