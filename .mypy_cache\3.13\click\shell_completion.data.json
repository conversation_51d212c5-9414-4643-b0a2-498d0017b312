{".class": "MypyFile", "_fullname": "click.shell_completion", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Argument": {".class": "SymbolTableNode", "cross_ref": "click.core.Argument", "kind": "Gdef"}, "BashComplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.shell_completion.ShellComplete"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.shell_completion.BashComplete", "name": "BashComplete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.shell_completion.BashComplete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.shell_completion", "mro": ["click.shell_completion.BashComplete", "click.shell_completion.ShellComplete", "builtins.object"], "names": {".class": "SymbolTable", "_check_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "click.shell_completion.BashComplete._check_version", "name": "_check_version", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_version of BashComplete", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "click.shell_completion.BashComplete._check_version", "name": "_check_version", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_version of BashComplete", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.BashComplete.format_completion", "name": "format_completion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["click.shell_completion.BashComplete", "click.shell_completion.CompletionItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_completion of BashComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_completion_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.BashComplete.get_completion_args", "name": "get_completion_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.BashComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completion_args of BashComplete", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.BashComplete.name", "name": "name", "type": "builtins.str"}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.BashComplete.source", "name": "source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.BashComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "source of BashComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.BashComplete.source_template", "name": "source_template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.BashComplete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.shell_completion.BashComplete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Command": {".class": "SymbolTableNode", "cross_ref": "click.core.Command", "kind": "Gdef"}, "CompletionItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.shell_completion.CompletionItem", "name": "CompletionItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.shell_completion.CompletionItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.shell_completion", "mro": ["click.shell_completion.CompletionItem", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.CompletionItem.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["click.shell_completion.CompletionItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of CompletionItem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "value", "type", "help", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.CompletionItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "value", "type", "help", "kwargs"], "arg_types": ["click.shell_completion.CompletionItem", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CompletionItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "click.shell_completion.CompletionItem.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.shell_completion.CompletionItem._info", "name": "_info", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "help": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.shell_completion.CompletionItem.help", "name": "help", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.shell_completion.CompletionItem.type", "name": "type", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.shell_completion.CompletionItem.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.CompletionItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.shell_completion.CompletionItem", "values": [], "variance": 0}, "slots": ["_info", "help", "type", "value"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Context": {".class": "SymbolTableNode", "cross_ref": "click.core.Context", "kind": "Gdef"}, "FishComplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.shell_completion.ShellComplete"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.shell_completion.FishComplete", "name": "FishComplete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.shell_completion.FishComplete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.shell_completion", "mro": ["click.shell_completion.FishComplete", "click.shell_completion.ShellComplete", "builtins.object"], "names": {".class": "SymbolTable", "format_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.FishComplete.format_completion", "name": "format_completion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["click.shell_completion.FishComplete", "click.shell_completion.CompletionItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_completion of FishComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_completion_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.FishComplete.get_completion_args", "name": "get_completion_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.FishComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completion_args of FishComplete", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.FishComplete.name", "name": "name", "type": "builtins.str"}}, "source_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.FishComplete.source_template", "name": "source_template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.FishComplete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.shell_completion.FishComplete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Group": {".class": "SymbolTableNode", "cross_ref": "click.core.Group", "kind": "Gdef"}, "Option": {".class": "SymbolTableNode", "cross_ref": "click.core.Option", "kind": "Gdef"}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "click.core.Parameter", "kind": "Gdef"}, "ParameterSource": {".class": "SymbolTableNode", "cross_ref": "click.core.ParameterSource", "kind": "Gdef"}, "ShellComplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.shell_completion.ShellComplete", "name": "ShellComplete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.shell_completion", "mro": ["click.shell_completion.ShellComplete", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cli", "ctx_args", "prog_name", "complete_var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cli", "ctx_args", "prog_name", "complete_var"], "arg_types": ["click.shell_completion.ShellComplete", "click.core.Command", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShellComplete", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cli": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.shell_completion.ShellComplete.cli", "name": "cli", "type": "click.core.Command"}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.complete", "name": "complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete of ShellComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "complete_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.shell_completion.ShellComplete.complete_var", "name": "complete_var", "type": "builtins.str"}}, "ctx_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.shell_completion.ShellComplete.ctx_args", "name": "ctx_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "format_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.format_completion", "name": "format_completion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["click.shell_completion.ShellComplete", "click.shell_completion.CompletionItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_completion of ShellComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "func_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.shell_completion.ShellComplete.func_name", "name": "func_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "func_name of ShellComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.shell_completion.ShellComplete.func_name", "name": "func_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "func_name of ShellComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_completion_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.get_completion_args", "name": "get_completion_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completion_args of ShellComplete", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "incomplete"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.get_completions", "name": "get_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "args", "incomplete"], "arg_types": ["click.shell_completion.ShellComplete", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completions of ShellComplete", "ret_type": {".class": "Instance", "args": ["click.shell_completion.CompletionItem"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "click.shell_completion.ShellComplete.name", "name": "name", "type": "builtins.str"}}, "prog_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.shell_completion.ShellComplete.prog_name", "name": "prog_name", "type": "builtins.str"}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.source", "name": "source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "source of ShellComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "click.shell_completion.ShellComplete.source_template", "name": "source_template", "type": "builtins.str"}}, "source_vars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ShellComplete.source_vars", "name": "source_vars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ShellComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "source_vars of ShellComplete", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ShellComplete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.shell_completion.ShellComplete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShellCompleteType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ShellCompleteType", "name": "ShellCompleteType", "upper_bound": {".class": "TypeType", "item": "click.shell_completion.ShellComplete"}, "values": [], "variance": 0}}, "ZshComplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.shell_completion.ShellComplete"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.shell_completion.ZshComplete", "name": "ZshComplete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.shell_completion.ZshComplete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.shell_completion", "mro": ["click.shell_completion.ZshComplete", "click.shell_completion.ShellComplete", "builtins.object"], "names": {".class": "SymbolTable", "format_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ZshComplete.format_completion", "name": "format_completion", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["click.shell_completion.ZshComplete", "click.shell_completion.CompletionItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_completion of ZshComplete", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_completion_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.ZshComplete.get_completion_args", "name": "get_completion_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.shell_completion.ZshComplete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completion_args of ZshComplete", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.ZshComplete.name", "name": "name", "type": "builtins.str"}}, "source_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion.ZshComplete.source_template", "name": "source_template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ZshComplete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.shell_completion.ZshComplete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_": {".class": "SymbolTableNode", "cross_ref": "gettext.gettext", "kind": "Gdef"}, "_SOURCE_BASH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion._SOURCE_BASH", "name": "_SOURCE_BASH", "type": "builtins.str"}}, "_SOURCE_FISH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion._SOURCE_FISH", "name": "_SOURCE_FISH", "type": "builtins.str"}}, "_SOURCE_ZSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click.shell_completion._SOURCE_ZSH", "name": "_SOURCE_ZSH", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.shell_completion.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_available_shells": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "click.shell_completion._available_shells", "name": "_available_shells", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "click.shell_completion.ShellComplete"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_is_incomplete_argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion._is_incomplete_argument", "name": "_is_incomplete_argument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "param"], "arg_types": ["click.core.Context", "click.core.Parameter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_incomplete_argument", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_incomplete_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "args", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion._is_incomplete_option", "name": "_is_incomplete_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "args", "param"], "arg_types": ["click.core.Context", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "click.core.Parameter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_incomplete_option", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cli", "ctx_args", "prog_name", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion._resolve_context", "name": "_resolve_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cli", "ctx_args", "prog_name", "args"], "arg_types": ["click.core.Command", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_context", "ret_type": "click.core.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_incomplete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "args", "incomplete"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion._resolve_incomplete", "name": "_resolve_incomplete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["ctx", "args", "incomplete"], "arg_types": ["click.core.Context", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_incomplete", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["click.core.Command", "click.core.Parameter"], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_of_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion._start_of_option", "name": "_start_of_option", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "value"], "arg_types": ["click.core.Context", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_start_of_option", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_completion_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.add_completion_class", "name": "add_completion_class", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ShellCompleteType", "id": -1, "name": "ShellCompleteType", "namespace": "click.shell_completion.add_completion_class", "upper_bound": {".class": "TypeType", "item": "click.shell_completion.ShellComplete"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_completion_class", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ShellCompleteType", "id": -1, "name": "ShellCompleteType", "namespace": "click.shell_completion.add_completion_class", "upper_bound": {".class": "TypeType", "item": "click.shell_completion.ShellComplete"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.shell_completion.ShellCompleteType", "id": -1, "name": "ShellCompleteType", "namespace": "click.shell_completion.add_completion_class", "upper_bound": {".class": "TypeType", "item": "click.shell_completion.ShellComplete"}, "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cabc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "echo": {".class": "SymbolTableNode", "cross_ref": "click.utils.echo", "kind": "Gdef"}, "get_completion_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.get_completion_class", "name": "get_completion_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["shell"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completion_class", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "click.shell_completion.ShellComplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "shell_complete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cli", "ctx_args", "prog_name", "complete_var", "instruction"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.shell_complete", "name": "shell_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cli", "ctx_args", "prog_name", "complete_var", "instruction"], "arg_types": ["click.core.Command", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shell_complete", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_arg_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.shell_completion.split_arg_string", "name": "split_arg_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["string"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_arg_string", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\shell_completion.py"}