import logging
from app.config import <PERSON><PERSON>_CONFIG
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

logger = logging.getLogger(__name__)

# Initialise LLM with centralized config
llm = Together(**LLM_CONFIG)

# Summariser Prompt
SHORT_SUMMARY_PROMPT = """
You are an expert system design analyst.

Your task is to carefully read the provided Product Requirements Document (PRD) and generate a short, precise one-paragraph summary that clearly captures the main goal and the core purpose of the product.

Instructions:
- The summary must be between 2 to 4 sentences.
- Do not list features, detailed specifications, success metrics, constraints, or future scope.
- Do not restate or quote the PRD content.
- Do not include any markdown, bullet points, or formatting.
- Do not include the PRD text in your response.
- Do not repeat the PRD in any form.
- Return only the summary as plain text.

Here is the PRD:
"""


# Retry configuration: max 3 attempts with exponential backoff
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
    reraise=True,
)
async def invoke_llm(prompt: str):
    result = await llm.ainvoke(prompt)

    if not result or len(result.strip()) == 0:
        raise Exception("Empty response from LLM.")

    return result.strip()


def clean_summary_response(response: str):
    cleaned = response.split("```")[0].strip()
    if "Here is the PRD" in cleaned:
        cleaned = cleaned.split("Here is the PRD")[0].strip()
    return cleaned


async def summarise_prd(prd_text: str):
    logger.info("Summarising PRD into short summary...")

    try:
        prompt = f"{SHORT_SUMMARY_PROMPT}\n\n{prd_text}\n\nSummary (Only plain text, do not restate the PRD):"

        raw_response = await invoke_llm(prompt)

        summary = clean_summary_response(raw_response)

        logger.info(f"Generated Summary:\n{summary}")
        return summary

    except Exception as e:
        logger.error(f"Summariser failed after retries: {str(e)}")
        raise Exception(
            "Summariser failed to generate short summary after retries."
        ) from e
