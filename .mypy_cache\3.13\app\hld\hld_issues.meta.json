{"data_mtime": 1753995103, "dep_lines": [5, 7, 1, 2, 3, 4, 6, 7, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.config", "tenacity.retry", "logging", "json", "asyncio", "typing", "langchain_together", "tenacity", "re", "builtins", "_frozen_importlib", "abc", "datetime", "langchain_core", "langchain_core.caches", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.language_models", "langchain_core.language_models.base", "langchain_core.language_models.llms", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables", "langchain_core.runnables.base", "langchain_together.llms", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.types", "tenacity.asyncio", "tenacity.asyncio.retry", "tenacity.stop", "tenacity.wait"], "hash": "453245424279d19458554ac9a0b35988c721423d", "id": "app.hld.hld_issues", "ignore_all": true, "interface_hash": "ad7352317d2edbeef9740bd41a62dca125a0d6ea", "mtime": 1753216542, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\AutoSpecDesign\\app\\hld\\hld_issues.py", "plugin_data": null, "size": 5684, "suppressed": [], "version_id": "1.15.0"}