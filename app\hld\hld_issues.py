import logging
import json
import async<PERSON>
from typing import Dict, List, Any
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1024,
    together_api_key=TOGETHER_API_KEY,
)

# Issues Generation Prompt
ISSUES_PROMPT = """
You are an expert system architect. Based on the PRD and HLD components, identify potential issues, challenges, and areas of concern in the system design.

Requirements:
1. Identify technical issues (performance, scalability, security)
2. Identify architectural issues (design patterns, component coupling)
3. Identify operational issues (deployment, monitoring, maintenance)
4. Identify business issues (user experience, compliance, cost)
5. Provide recommendations for each issue
- Return ONLY a valid JSON object, no explanations, no markdown, no code blocks, no step-by-step, no final answer, no boxed answer, no extra text. If you cannot answer, return an empty object {}.

Example:
{
  "technical_issues": [
    {"issue": "Potential performance bottlenecks under high load", "severity": "medium", "impact": "User experience degradation", "recommendation": "Implement caching and load balancing"}
  ],
  "architectural_issues": [
    {"issue": "Tight coupling between components", "severity": "medium", "impact": "Difficult to maintain and scale", "recommendation": "Implement loose coupling and interfaces"}
  ],
  "operational_issues": [
    {"issue": "Limited monitoring and alerting", "severity": "high", "impact": "Difficult to detect and resolve issues", "recommendation": "Implement comprehensive monitoring"}
  ],
  "business_issues": [
    {"issue": "Security vulnerabilities in user data", "severity": "critical", "impact": "Potential data breaches", "recommendation": "Implement robust security measures"}
  ],
  "priority_actions": [
    {"action": "Security audit and implementation", "timeline": "Immediate", "resources": "Security expert, penetration testing"}
  ]
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

def extract_json_object_strict(llm_response: str):
    try:
        cleaned = llm_response.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)  # Remove headings
        cleaned = re.sub(r"(The final answer is:|Step \\d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        match = re.search(r"\{.*\}", cleaned, re.DOTALL)
        if not match:
            return {}
        json_str = match.group(0)
        parsed = json.loads(json_str)
        return parsed if isinstance(parsed, dict) else {}
    except Exception as e:
        logger.error(f"Error during JSON object extraction: {str(e)}")
        logger.error(f"Raw LLM response: {llm_response}")
        return {}

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_issues(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    """Generate system issues and challenges."""
    logger.info("Generating issues and challenges...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = ISSUES_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        response = await asyncio.get_event_loop().run_in_executor(None, llm.invoke, prompt)
        logger.info(f"Raw LLM output for issues: {response}")
        issues = extract_json_object_strict(response)
        logger.info("Issues generated successfully")
        return issues
    except Exception as e:
        logger.error(f"Failed to generate issues: {str(e)}")
        raise

def get_fallback_issues() -> Dict[str, Any]:
    """Get fallback issues when generation fails."""
    return {
        "technical_issues": [
            {
                "issue": "Potential performance bottlenecks under high load",
                "severity": "medium",
                "impact": "User experience degradation",
                "recommendation": "Implement caching and load balancing"
            }
        ],
        "architectural_issues": [
            {
                "issue": "Tight coupling between components",
                "severity": "medium",
                "impact": "Difficult to maintain and scale",
                "recommendation": "Implement loose coupling and interfaces"
            }
        ],
        "operational_issues": [
            {
                "issue": "Limited monitoring and alerting",
                "severity": "high",
                "impact": "Difficult to detect and resolve issues",
                "recommendation": "Implement comprehensive monitoring"
            }
        ],
        "business_issues": [
            {
                "issue": "Security vulnerabilities in user data",
                "severity": "critical",
                "impact": "Potential data breaches",
                "recommendation": "Implement robust security measures"
            }
        ],
        "priority_actions": [
            {
                "action": "Security audit and implementation",
                "timeline": "Immediate",
                "resources": "Security expert, penetration testing"
            }
        ]
    } 