# AutoSpec Design - Backend API Documentation

## 🎯 Overview

This document provides comprehensive API documentation for frontend developers integrating with the AutoSpec Design backend.

## 🔗 Base URL
```
http://localhost:8000
```

## 📋 API Endpoints

### HLD (High-Level Design) Endpoints

#### 1. Generate HLD Narrative
```http
POST /api/hld/narrative
```

**Request Body:**
```json
{
  "clean_text": "Your PRD text content",
  "structured_prd": "Structured PRD summary"
}
```

**Response:**
```json
{
  "status": "success",
  "hld_narrative": "Generated HLD narrative text..."
}
```

#### 2. Generate HLD Components
```http
POST /api/hld/components
```

**Request Body:**
```json
{
  "structured_prd": "PRD summary",
  "hld_narrative": "HLD narrative text"
}
```

**Response:**
```json
{
  "status": "success",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role",
      "responsibilities": ["Responsibility 1", "Responsibility 2"]
    }
  ]
}
```

#### 3. Generate Sequence Diagram
```http
POST /api/hld/sequence
```

**Request Body:**
```json
{
  "structured_prd": "PRD summary",
  "hld_narrative": "HLD narrative text"
}
```

**Response:**
```json
{
  "status": "success",
  "mermaid": "sequenceDiagram\n    participant A\n    participant B\n    A->>B: Hello"
}
```

#### 4. Generate Tech Stack
```http
POST /api/hld/tech-stack
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "tech_stack": {
    "frontend": {
      "framework": "React",
      "libraries": ["React Router", "Axios"],
      "styling": "Tailwind CSS",
      "state_management": "Context API"
    },
    "backend": {
      "language": "Node.js",
      "framework": "Express",
      "libraries": ["JWT", "bcrypt"],
      "api_design": "REST"
    },
    "database": {
      "primary": "PostgreSQL",
      "caching": "Redis",
      "search": "Elasticsearch"
    },
    "infrastructure": {
      "hosting": "AWS",
      "containerization": "Docker",
      "ci_cd": "GitHub Actions",
      "monitoring": "Prometheus"
    },
    "reasoning": "Why this tech stack is recommended"
  }
}
```

#### 5. Generate Assumptions
```http
POST /api/hld/assumptions
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "assumptions": {
    "technical_assumptions": [
      {
        "assumption": "System will handle moderate user load",
        "impact": "Design for horizontal scaling",
        "risk_level": "medium"
      }
    ],
    "business_assumptions": [...],
    "security_assumptions": [...],
    "operational_assumptions": [...],
    "risk_mitigation": [...]
  }
}
```

#### 6. Generate Issues
```http
POST /api/hld/issues
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "issues": {
    "technical_issues": [
      {
        "issue": "Potential performance bottlenecks",
        "severity": "medium",
        "impact": "User experience degradation",
        "recommendation": "Implement caching and load balancing"
      }
    ],
    "architectural_issues": [...],
    "operational_issues": [...],
    "business_issues": [...],
    "priority_actions": [...]
  }
}
```

### LLD (Low-Level Design) Endpoints

#### 1. Generate Database Schema
```http
POST /api/lld/database-schema
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "database_schema": {
    "database_type": "PostgreSQL",
    "tables": [
      {
        "name": "users",
        "description": "User account information",
        "columns": [
          {
            "name": "id",
            "type": "UUID",
            "constraints": ["PRIMARY KEY"],
            "description": "Unique user identifier"
          }
        ],
        "indexes": [...],
        "relationships": [...]
      }
    ],
    "ddl_statements": ["CREATE TABLE users (...);"]
  }
}
```

#### 2. Generate Class Structure
```http
POST /api/lld/class-structure
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "class_structure": {
    "classes": [
      {
        "name": "UserService",
        "description": "Handles user-related operations",
        "attributes": [
          {
            "name": "db_connection",
            "type": "DatabaseConnection",
            "access": "private",
            "description": "Database connection"
          }
        ],
        "methods": [
          {
            "name": "create_user",
            "parameters": [
              {
                "name": "email",
                "type": "str",
                "description": "User email"
              }
            ],
            "return_type": "User",
            "description": "Creates a new user"
          }
        ],
        "relationships": [...]
      }
    ],
    "design_patterns": [
      {
        "pattern": "Repository",
        "description": "Data access abstraction",
        "classes_involved": ["UserService", "UserRepository"]
      }
    ]
  }
}
```

#### 3. Generate DML Statements
```http
POST /api/lld/dml-statements
```

**Request Body:**
```json
{
  "database_schema": {
    "database_type": "PostgreSQL",
    "tables": [...]
  }
}
```

**Response:**
```json
{
  "status": "success",
  "dml_statements": {
    "insert_statements": [
      {
        "table": "users",
        "description": "Create a new user",
        "sql": "INSERT INTO users (id, email, created_at) VALUES (gen_random_uuid(), '<EMAIL>', NOW());"
      }
    ],
    "update_statements": [...],
    "delete_statements": [...],
    "sample_queries": [...]
  }
}
```

#### 4. Generate Complete LLD
```http
POST /api/lld/complete
```

**Request Body:**
```json
{
  "prd_summary": "PRD summary",
  "hld_components": [
    {
      "name": "Component Name",
      "role": "Component role"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "complete_lld": {
    "database_schema": {...},
    "class_structure": {...},
    "dml_statements": {...},
    "generated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 🔄 Error Handling

All endpoints return consistent error responses:

```json
{
  "status": "error",
  "message": "Error description"
}
```

Common error scenarios:
- **Missing required fields** in request body
- **AI generation failures** (with fallback mechanisms)
- **Rate limiting** from Together AI
- **Server errors**

## ⏱️ Rate Limiting

The API respects Together AI rate limits:
- **6 queries per minute**
- **60,000 tokens per minute**

Frontend should implement:
- **Request queuing** for multiple operations
- **User feedback** during processing
- **Retry logic** with exponential backoff

## 🎨 Frontend Integration Examples

### React/Next.js Example

```javascript
// HLD Narrative Generation
const generateHLDNarrative = async (prdText, structuredPRD) => {
  try {
    const response = await fetch('http://localhost:8000/api/hld/narrative', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        clean_text: prdText,
        structured_prd: structuredPRD
      })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      return data.hld_narrative;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('HLD Narrative generation failed:', error);
    throw error;
  }
};

// Complete LLD Generation
const generateCompleteLLD = async (prdSummary, hldComponents) => {
  try {
    const response = await fetch('http://localhost:8000/api/lld/complete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prd_summary: prdSummary,
        hld_components: hldComponents
      })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      return data.complete_lld;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('Complete LLD generation failed:', error);
    throw error;
  }
};
```

### Vue.js Example

```javascript
// Composition API
import { ref } from 'vue'

export function useHLDGeneration() {
  const hldNarrative = ref('')
  const loading = ref(false)
  const error = ref(null)

  const generateNarrative = async (prdText, structuredPRD) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('http://localhost:8000/api/hld/narrative', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clean_text: prdText,
          structured_prd: structuredPRD
        })
      })
      
      const data = await response.json()
      
      if (data.status === 'success') {
        hldNarrative.value = data.hld_narrative
      } else {
        error.value = data.message
      }
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  return {
    hldNarrative,
    loading,
    error,
    generateNarrative
  }
}
```

## 🚀 Best Practices

### 1. Request Management
- **Queue requests** to avoid rate limiting
- **Show loading states** during AI generation
- **Implement retry logic** for failed requests

### 2. Error Handling
- **Graceful degradation** when AI fails
- **User-friendly error messages**
- **Fallback to cached data** when possible

### 3. Performance
- **Debounce user inputs** to reduce API calls
- **Cache successful responses**
- **Implement optimistic updates**

### 4. User Experience
- **Real-time progress indicators**
- **Copy-to-clipboard functionality**
- **Export options** (JSON, PDF, etc.)

## 🔧 Development Setup

1. **Start the backend server:**
   ```bash
   uvicorn app.main:app --reload
   ```

2. **Test endpoints:**
   - Visit http://localhost:8000/docs for interactive API docs
   - Use the examples above for integration

3. **Monitor logs:**
   - Check console for API request/response logs
   - Monitor Together AI rate limits

## 📞 Support

For API issues:
- Check the interactive docs at `/docs`
- Review error messages in response
- Check server logs for detailed errors
- Ensure environment variables are set correctly

---

**Ready for frontend development!** 🎨✨ 