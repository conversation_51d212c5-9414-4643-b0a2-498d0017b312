import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld/assumptions"
CLEAN_TEXT = "Product Requirements Document: E-commerce Platform\n... (shortened for brevity) ..."
STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, and payments."

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        # Get narrative and components first
        n_resp = await client.post("http://localhost:8000/api/hld/narrative", json={
            "clean_text": CLEAN_TEXT,
            "structured_prd": STRUCTURED_PRD
        })
        n_data = n_resp.json()
        hld_narrative = n_data.get("hld_narrative", "")
        c_resp = await client.post("http://localhost:8000/api/hld/components", json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": hld_narrative
        })
        c_data = c_resp.json()
        hld_components = c_data.get("hld_components", [])
        print("/hld/components response:", c_data)
        print("\n--- Testing /hld/assumptions ---")
        resp = await client.post(API_URL, json={
            "prd_summary": STRUCTURED_PRD,
            "hld_components": hld_components
        })
        data = resp.json()
        print("/hld/assumptions response:", data)
        ok = data.get("status") == "success" and "assumptions" in data
        if ok:
            print("[PASS] /hld/assumptions - assumptions present.")
        else:
            print(f"[FAIL] /hld/assumptions - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 