import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld/components"
CLEAN_TEXT = """
Product Requirements Document: E-commerce Platform

Overview:
This document outlines the requirements for a modern e-commerce platform that enables businesses to sell products online with advanced features for both customers and administrators.

Core Features:
1. User Authentication System
   - JWT-based authentication
   - Role-based access control (Customer, Admin, Manager)
   - Password reset functionality
   - Two-factor authentication (optional)

2. Product Management
   - Product catalog with categories
   - Inventory tracking
   - Product search and filtering
   - Image upload and management
   - Pricing and discount management

3. Shopping Cart & Checkout
   - Add/remove items from cart
   - Quantity management
   - Price calculation with taxes
   - Multiple payment methods (Credit Card, PayPal, Stripe)
   - Order confirmation and tracking

4. User Management
   - Customer registration and profiles
   - Address management
   - Order history
   - Wishlist functionality
   - Customer reviews and ratings

5. Admin Dashboard
   - Sales analytics and reporting
   - Inventory management
   - Order processing
   - Customer management
   - Content management system

Technical Requirements:
- Frontend: React.js with TypeScript
- Backend: Node.js with Express
- Database: PostgreSQL with Redis caching
- Payment Processing: Stripe integration
- File Storage: AWS S3 for images
- Search: Elasticsearch for product search
- Email: SendGrid for notifications

Performance Requirements:
- Page load time: < 3 seconds
- Support for 10,000 concurrent users
- 99.9% uptime
- Mobile-responsive design

Security Requirements:
- HTTPS encryption
- PCI DSS compliance for payments
- SQL injection prevention
- XSS protection
- CSRF protection
"""
STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, shopping cart, admin dashboard, and payments."

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        # Get narrative first
        n_resp = await client.post("http://localhost:8000/api/hld/narrative", json={
            "clean_text": CLEAN_TEXT,
            "structured_prd": STRUCTURED_PRD
        })
        n_data = n_resp.json()
        hld_narrative = n_data.get("hld_narrative", "")
        print("/hld/narrative response:", n_data)
        print("\n--- Testing /hld/components ---")
        resp = await client.post(API_URL, json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": hld_narrative
        })
        data = resp.json()
        print("/hld/components response:", data)
        ok = data.get("status") == "success" and isinstance(data.get("hld_components"), list)
        if ok:
            print("[PASS] /hld/components - hld_components present.")
        else:
            print(f"[FAIL] /hld/components - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 