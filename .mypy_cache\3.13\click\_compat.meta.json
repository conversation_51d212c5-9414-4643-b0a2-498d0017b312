{"data_mtime": 1753995759, "dep_lines": [4, 513, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 20, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "click._winconsole", "__future__", "codecs", "collections", "io", "os", "re", "sys", "typing", "types", "weakref", "builtins", "_frozen_importlib", "_io", "abc", "enum"], "hash": "3fb619ba82fdde36783971f1de603b7790220677", "id": "click._compat", "ignore_all": true, "interface_hash": "bcdb8582df07c044952b59e24f13a7e92ef43560", "mtime": 1753995713, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\_compat.py", "plugin_data": null, "size": 18693, "suppressed": [], "version_id": "1.15.0"}