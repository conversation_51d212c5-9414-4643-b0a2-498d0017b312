{"data_mtime": 1753995761, "dep_lines": [3, 7, 8, 1, 3, 4, 5, 122, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "click._compat", "click.parser", "__future__", "collections", "contextlib", "gettext", "shutil", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing"], "hash": "039bd3df43b97cbd8103bfdd8edd3a6f93f0d104", "id": "click.formatting", "ignore_all": true, "interface_hash": "cd9cc6164109071ae0a2d1404dffa423be83d01b", "mtime": 1753995713, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\formatting.py", "plugin_data": null, "size": 9726, "suppressed": [], "version_id": "1.15.0"}