import asyncio
import httpx

API_URL = "http://localhost:8000/api/lld/database-schema"

PRD_SUMMARY = "E-commerce platform with user authentication, product catalog, and payments."
HLD_COMPONENTS = [
    {"name": "User Service", "role": "Handles user registration and authentication"},
    {"name": "Product Catalog", "role": "Manages product listings and inventory"},
    {"name": "Order Service", "role": "Handles order placement and tracking"},
    {"name": "Payment Gateway", "role": "Processes payments securely"}
]

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /database-schema ---")
        resp = await client.post(API_URL, json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        print("/database-schema response:", data)
        ok = data.get("status") == "success" and "database_schema" in data
        if ok:
            schema = data["database_schema"]
            required_keys = ["database_type", "tables", "ddl_statements"]
            missing = [k for k in required_keys if k not in schema]
            if missing:
                print(f"[FAIL] /database-schema - Missing keys in database_schema: {missing}")
            else:
                print("[PASS] /database-schema - All required keys present.")
        else:
            print(f"[FAIL] /database-schema - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 