import asyncio
import httpx

API_URL = "http://localhost:8000/api/lld"

PRD_SUMMARY = "E-commerce platform with user authentication, product catalog, and payments."
HLD_COMPONENTS = [
    {"name": "User Service", "role": "Handles user registration and authentication"},
    {"name": "Product Catalog", "role": "Manages product listings and inventory"},
    {"name": "Order Service", "role": "Handles order placement and tracking"},
    {"name": "Payment Gateway", "role": "Processes payments securely"}
]

async def main():
    results = []
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /database-schema ---")
        resp = await client.post(f"{API_URL}/database-schema", json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        ok = data.get("status") == "success" and "database_schema" in data
        results.append(("/database-schema", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /class-structure ---")
        resp = await client.post(f"{API_URL}/class-structure", json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        ok = data.get("status") == "success" and "class_structure" in data
        results.append(("/class-structure", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /dml-statements ---")
        resp = await client.post(f"{API_URL}/dml-statements", json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        ok = data.get("status") == "success" and "dml_statements" in data
        results.append(("/dml-statements", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /complete ---")
        resp = await client.post(f"{API_URL}/complete", json={
            "prd_summary": PRD_SUMMARY,
            "hld_components": HLD_COMPONENTS
        })
        data = resp.json()
        ok = data.get("status") == "success" and "complete_lld" in data
        results.append(("/complete", ok, data))

    print("\n===== LLD API Test Summary =====")
    for route, ok, data in results:
        if ok:
            print(f"[PASS] {route}")
        else:
            print(f"[FAIL] {route} - Response: {data}")
    print("===============================\n")

if __name__ == "__main__":
    asyncio.run(main()) 