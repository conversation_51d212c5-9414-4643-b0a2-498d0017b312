import logging
import json
import async<PERSON>
from typing import Dict, List, Any
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1024,
    together_api_key=TOGETHER_API_KEY,
)

# Assumptions Generation Prompt
ASSUMPTIONS_PROMPT = """
You are an expert system architect. Based on the PRD and HLD components, identify key assumptions and constraints for the system design.

Requirements:
1. Identify technical assumptions (infrastructure, performance, scalability)
2. Identify business assumptions (user behavior, data volume, growth)
3. Identify security and compliance assumptions
4. Identify operational assumptions (maintenance, monitoring, support)
5. Consider risks and mitigation strategies
- Return ONLY a valid JSON object, no explanations, no markdown, no code blocks, no step-by-step, no final answer, no boxed answer, no extra text. If you cannot answer, return an empty object {}.

Example:
{
  "technical_assumptions": [
    {"assumption": "System will handle moderate user load", "impact": "Design for horizontal scaling", "risk_level": "medium"}
  ],
  "business_assumptions": [
    {"assumption": "Users expect fast response times", "impact": "Implement caching and optimization", "risk_level": "high"}
  ],
  "security_assumptions": [
    {"assumption": "Standard security practices apply", "impact": "Implement authentication and authorization", "risk_level": "high"}
  ],
  "operational_assumptions": [
    {"assumption": "24/7 system availability required", "impact": "Design for high availability", "risk_level": "medium"}
  ],
  "risk_mitigation": [
    {"risk": "Data loss or corruption", "mitigation": "Implement backup and recovery procedures"}
  ]
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

def extract_json_object_strict(llm_response: str):
    import re, json
    try:
        cleaned = llm_response.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)  # Remove headings
        cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        match = re.search(r"\{.*\}", cleaned, re.DOTALL)
        if not match:
            return {}
        json_str = match.group(0)
        parsed = json.loads(json_str)
        return parsed if isinstance(parsed, dict) else {}
    except Exception as e:
        logger.error(f"Error during JSON object extraction: {str(e)}")
        logger.error(f"Raw LLM response: {llm_response}")
        return {}

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_assumptions(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    """Generate system assumptions and constraints."""
    logger.info("Generating assumptions and constraints...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = ASSUMPTIONS_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        response = await asyncio.get_event_loop().run_in_executor(None, llm.invoke, prompt)
        logger.info(f"Raw LLM output for assumptions: {response}")
        assumptions = extract_json_object_strict(response)
        logger.info("Assumptions generated successfully")
        return assumptions
    except Exception as e:
        logger.error(f"Failed to generate assumptions: {str(e)}")
        raise

def get_fallback_assumptions() -> Dict[str, Any]:
    """Get fallback assumptions when generation fails."""
    return {
        "technical_assumptions": [
            {
                "assumption": "System will handle moderate user load",
                "impact": "Design for horizontal scaling",
                "risk_level": "medium"
            }
        ],
        "business_assumptions": [
            {
                "assumption": "Users expect fast response times",
                "impact": "Implement caching and optimization",
                "risk_level": "high"
            }
        ],
        "security_assumptions": [
            {
                "assumption": "Standard security practices apply",
                "impact": "Implement authentication and authorization",
                "risk_level": "high"
            }
        ],
        "operational_assumptions": [
            {
                "assumption": "24/7 system availability required",
                "impact": "Design for high availability",
                "risk_level": "medium"
            }
        ],
        "risk_mitigation": [
            {
                "risk": "Data loss or corruption",
                "mitigation": "Implement backup and recovery procedures"
            }
        ]
    } 