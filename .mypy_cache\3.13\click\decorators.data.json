{".class": "MypyFile", "_fullname": "click.decorators", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Argument": {".class": "SymbolTableNode", "cross_ref": "click.core.Argument", "kind": "Gdef"}, "CmdType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "name": "CmdType", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, "Command": {".class": "SymbolTableNode", "cross_ref": "click.core.Command", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "click.core.Context", "kind": "Gdef"}, "FC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "name": "FC", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "Group": {".class": "SymbolTableNode", "cross_ref": "click.core.Group", "kind": "Gdef"}, "GrpType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "name": "GrpType", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, "Option": {".class": "SymbolTableNode", "cross_ref": "click.core.Option", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "click.core.Parameter", "kind": "Gdef"}, "R": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "name": "R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_": {".class": "SymbolTableNode", "cross_ref": "gettext.gettext", "kind": "Gdef"}, "_AnyCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "click.decorators._AnyCallable", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.decorators.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_param_memo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["f", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators._param_memo", "name": "_param_memo", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["f", "param"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "click.core.Parameter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_param_memo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 4], "arg_names": ["param_decls", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.argument", "name": "argument", "type": {".class": "CallableType", "arg_kinds": [2, 5, 4], "arg_names": ["param_decls", "cls", "attrs"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "click.core.Argument"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "argument", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "click.decorators.command", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "UnionType", "items": ["click.core.Command", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["click.core.Command", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command", "upper_bound": "click.core.Command", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#1", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.CmdType", "id": -1, "name": "CmdType", "namespace": "click.decorators.command#2", "upper_bound": "click.core.Command", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "command", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Command", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "confirmation_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.confirmation_option", "name": "confirmation_option", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmation_option", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "echo": {".class": "SymbolTableNode", "cross_ref": "click.utils.echo", "kind": "Gdef"}, "get_current_context": {".class": "SymbolTableNode", "cross_ref": "click.globals.get_current_context", "kind": "Gdef"}, "group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "click.decorators.group", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "UnionType", "items": ["click.core.Group", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["click.core.Group", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group", "upper_bound": "click.core.Group", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.decorators.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#1", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [1, 3, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.GrpType", "id": -1, "name": "GrpType", "namespace": "click.decorators.group#2", "upper_bound": "click.core.Group", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["name", "cls", "attrs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "click.core.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "help_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.help_option", "name": "help_option", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "help_option", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "make_pass_decorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["object_type", "ensure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.make_pass_decorator", "name": "make_pass_decorator", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["object_type", "ensure"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "click.decorators.make_pass_decorator", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_pass_decorator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "click.decorators.make_pass_decorator", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "click.decorators.make_pass_decorator", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 4], "arg_names": ["param_decls", "cls", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.option", "name": "option", "type": {".class": "CallableType", "arg_kinds": [2, 5, 4], "arg_names": ["param_decls", "cls", "attrs"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "click.core.Option"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "option", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pass_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.pass_context", "name": "pass_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": ["click.core.Context", {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -1, "name": "P", "namespace": "click.decorators.pass_context", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -1, "name": "P", "namespace": "click.decorators.pass_context", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -2, "name": "R", "namespace": "click.decorators.pass_context", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pass_context", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -1, "name": "P", "namespace": "click.decorators.pass_context", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -1, "name": "P", "namespace": "click.decorators.pass_context", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -2, "name": "R", "namespace": "click.decorators.pass_context", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "click.decorators.P", "id": -1, "name": "P", "namespace": "click.decorators.pass_context", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -2, "name": "R", "namespace": "click.decorators.pass_context", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "pass_meta_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["key", "doc_description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.pass_meta_key", "name": "pass_meta_key", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["key", "doc_description"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pass_meta_key", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pass_obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.pass_obj", "name": "pass_obj", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "click.decorators.pass_obj", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "click.decorators.pass_obj", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "click.decorators.pass_obj", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "click.decorators.pass_obj", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pass_obj", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "click.decorators.pass_obj", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "click.decorators.pass_obj", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "click.decorators.pass_obj", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.T", "id": -1, "name": "T", "namespace": "click.decorators.pass_obj", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "click.decorators.P", "id": -2, "name": "P", "namespace": "click.decorators.pass_obj", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.R", "id": -3, "name": "R", "namespace": "click.decorators.pass_obj", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "password_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.password_option", "name": "password_option", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["param_decls", "kwargs"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "password_option", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "update_wrapper": {".class": "SymbolTableNode", "cross_ref": "functools.update_wrapper", "kind": "Gdef"}, "version_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 2, 5, 5, 5, 4], "arg_names": ["version", "param_decls", "package_name", "prog_name", "message", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.decorators.version_option", "name": "version_option", "type": {".class": "CallableType", "arg_kinds": [1, 2, 5, 5, 5, 4], "arg_names": ["version", "param_decls", "package_name", "prog_name", "message", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version_option", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.decorators.FC", "id": -1, "name": "FC", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "click.decorators._AnyCallable"}, "click.core.Command"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\decorators.py"}