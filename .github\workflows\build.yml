name: Backend CI

on:
  push:
    branches: ["main", "dev"]
  pull_request:
    branches: ["main", "dev"]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Check server boots
        env:
          TOGETHER_API_KEY: ${{ secrets.TOGETHER_API_KEY }}
        run: |
          uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
          sleep 5  # give it time to boot
          kill %1  # kill the background server
