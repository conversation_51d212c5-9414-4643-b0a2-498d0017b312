import os
import io
import zipfile
import fitz  # PyMuPDF
from docx import Document

# Supported formats
SUPPORTED_EXTENSIONS = [".pdf", ".txt", ".docx", ".zip"]


def extract_text_from_file(file_path: str) -> str:
    ext = os.path.splitext(file_path)[1].lower()

    if ext == ".pdf":
        return extract_text_from_pdf(file_path)
    elif ext == ".txt":
        return extract_text_from_txt(file_path)
    elif ext == ".docx":
        return extract_text_from_docx(file_path)
    elif ext == ".zip":
        return extract_text_from_zip(file_path)
    else:
        raise ValueError(f"Unsupported file format: {ext}")


def extract_text_from_pdf(path: str) -> str:
    doc = fitz.open(path)
    return "\n".join([page.get_text() for page in doc])


def extract_text_from_txt(path: str) -> str:
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def extract_text_from_docx(path: str) -> str:
    doc = Document(path)
    return "\n".join([para.text for para in doc.paragraphs])


def extract_text_from_pdf_bytes(pdf_bytes: bytes) -> str:
    doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    return "\n".join([page.get_text() for page in doc])


def extract_text_from_txt_bytes(txt_bytes: bytes) -> str:
    return txt_bytes.decode("utf-8", errors="ignore")


def extract_text_from_docx_bytes(docx_bytes: bytes) -> str:
    doc = Document(io.BytesIO(docx_bytes))
    return "\n".join([para.text for para in doc.paragraphs])


def extract_text_from_zip(zip_path: str) -> str:
    extracted_text = []

    with zipfile.ZipFile(zip_path, "r") as zip_ref:
        for filename in zip_ref.namelist():
            ext = os.path.splitext(filename)[1].lower()

            if ext not in SUPPORTED_EXTENSIONS:
                print(f"[SKIP] Unsupported file in zip: {filename}")
                continue

            try:
                with zip_ref.open(filename) as file:
                    file_bytes = file.read()

                    if ext == ".pdf":
                        text = extract_text_from_pdf_bytes(file_bytes)
                    elif ext == ".txt":
                        text = extract_text_from_txt_bytes(file_bytes)
                    elif ext == ".docx":
                        text = extract_text_from_docx_bytes(file_bytes)
                    else:
                        continue

                    extracted_text.append(f"\n--- File: {filename} ---\n{text}")

            except Exception as e:
                print(f"[WARN] Failed to process {filename}: {e}")

    return "\n".join(extracted_text)
