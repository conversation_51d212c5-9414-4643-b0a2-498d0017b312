import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld"

CLEAN_TEXT = """Product Requirements Document: E-commerce Platform\n... (shortened for brevity) ..."""
STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, and payments."

async def main():
    results = []
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /narrative ---")
        resp = await client.post(f"{API_URL}/narrative", json={
            "clean_text": CLEAN_TEXT,
            "structured_prd": STRUCTURED_PRD
        })
        data = resp.json()
        ok = data.get("status") == "success" and "hld_narrative" in data
        results.append(("/narrative", ok, data))
        hld_narrative = data.get("hld_narrative", "")
        await asyncio.sleep(12)

        print("\n--- Testing /components ---")
        resp = await client.post(f"{API_URL}/components", json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": hld_narrative
        })
        data = resp.json()
        ok = data.get("status") == "success" and isinstance(data.get("hld_components"), list)
        results.append(("/components", ok, data))
        hld_components = data.get("hld_components", [])
        await asyncio.sleep(12)

        print("\n--- Testing /sequence ---")
        resp = await client.post(f"{API_URL}/sequence", json={
            "structured_prd": STRUCTURED_PRD,
            "hld_narrative": hld_narrative
        })
        data = resp.json()
        ok = data.get("status") == "success" and "mermaid" in data
        results.append(("/sequence", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /tech-stack ---")
        resp = await client.post(f"{API_URL}/tech-stack", json={
            "prd_summary": STRUCTURED_PRD,
            "hld_components": hld_components
        })
        data = resp.json()
        ok = data.get("status") == "success" and "tech_stack" in data
        results.append(("/tech-stack", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /assumptions ---")
        resp = await client.post(f"{API_URL}/assumptions", json={
            "prd_summary": STRUCTURED_PRD,
            "hld_components": hld_components
        })
        data = resp.json()
        ok = data.get("status") == "success" and "assumptions" in data
        results.append(("/assumptions", ok, data))
        await asyncio.sleep(12)

        print("\n--- Testing /issues ---")
        resp = await client.post(f"{API_URL}/issues", json={
            "prd_summary": STRUCTURED_PRD,
            "hld_components": hld_components
        })
        data = resp.json()
        ok = data.get("status") == "success" and "issues" in data
        results.append(("/issues", ok, data))

    print("\n===== HLD API Test Summary =====")
    for route, ok, data in results:
        if ok:
            print(f"[PASS] {route}")
        else:
            print(f"[FAIL] {route} - Response: {data}")
    print("===============================\n")

if __name__ == "__main__":
    asyncio.run(main()) 