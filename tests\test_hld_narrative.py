import asyncio
import httpx

API_URL = "http://localhost:8000/api/hld/narrative"
CLEAN_TEXT = "Product Requirements Document: E-commerce Platform\n... (shortened for brevity) ..."
STRUCTURED_PRD = "E-commerce platform with user authentication, product catalog, and payments."

async def main():
    async with httpx.AsyncClient(timeout=90.0) as client:
        print("\n--- Testing /hld/narrative ---")
        resp = await client.post(API_URL, json={
            "clean_text": CLEAN_TEXT,
            "structured_prd": STRUCTURED_PRD
        })
        data = resp.json()
        print("/hld/narrative response:", data)
        ok = data.get("status") == "success" and "hld_narrative" in data
        if ok:
            print("[PASS] /hld/narrative - hld_narrative present.")
        else:
            print(f"[FAIL] /hld/narrative - Response: {data}")

if __name__ == "__main__":
    asyncio.run(main()) 