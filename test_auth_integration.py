#!/usr/bin/env python3
"""
AutoSpec Design - Authentication & Database Integration Test

This script demonstrates the complete authentication and database integration
including user management, document processing, and HLD/LLD generation with persistence.
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any
import os

# Base URL for your API
BASE_URL = "http://localhost:8000"

class AutoSpecClient:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = None
        self.auth_token = None
        self.user_info = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def signup(self, email: str, password: str, full_name: str = None, company: str = None) -> Dict[str, Any]:
        """Sign up a new user."""
        data = {
            "email": email,
            "password": password,
            "full_name": full_name,
            "company": company
        }
        
        async with self.session.post(f"{self.base_url}/api/auth/signup", json=data) as resp:
            result = await resp.json()
            if resp.status == 200:
                self.auth_token = result["access_token"]
                self.user_info = {
                    "user_id": result["user_id"],
                    "email": result["email"],
                    "role": result["role"]
                }
                print(f"✅ Signed up successfully: {email}")
            else:
                print(f"❌ Signup failed: {result}")
            return result
    
    async def login(self, email: str, password: str) -> Dict[str, Any]:
        """Login user."""
        data = {
            "email": email,
            "password": password
        }
        
        async with self.session.post(f"{self.base_url}/api/auth/login", json=data) as resp:
            result = await resp.json()
            if resp.status == 200:
                self.auth_token = result["access_token"]
                self.user_info = {
                    "user_id": result["user_id"],
                    "email": result["email"],
                    "role": result["role"]
                }
                print(f"✅ Logged in successfully: {email}")
            else:
                print(f"❌ Login failed: {result}")
            return result
    
    def get_headers(self) -> Dict[str, str]:
        """Get authentication headers."""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def get_profile(self) -> Dict[str, Any]:
        """Get user profile."""
        async with self.session.get(f"{self.base_url}/api/auth/me", headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200:
                print(f"✅ Profile fetched: {result.get('email', 'Unknown')}")
            return result
    
    async def upload_document(self, file_path: str, title: str) -> Dict[str, Any]:
        """Upload a document."""
        if not os.path.exists(file_path):
            # Create a sample PRD file for testing
            sample_content = """
# E-commerce Platform PRD

## Product Overview
We are building a modern e-commerce platform that allows users to browse products, add them to cart, and checkout securely.

## Core Features
1. User Authentication - JWT-based authentication system
2. Product Catalog - Browse and search products
3. Shopping Cart - Add/remove items from cart
4. Checkout Process - Secure payment processing
5. Order Management - Track and manage orders
6. Admin Dashboard - Manage products and orders

## Technical Requirements
- Frontend: React with TypeScript
- Backend: Node.js with Express
- Database: PostgreSQL
- Authentication: JWT tokens
- Payment: Stripe integration
- Hosting: AWS or similar cloud provider

## User Stories
- As a customer, I want to browse products so I can find items to purchase
- As a customer, I want to add items to cart so I can purchase multiple items
- As a customer, I want to checkout securely so I can complete my purchase
- As an admin, I want to manage products so I can control the catalog
            """
            with open(file_path, "w") as f:
                f.write(sample_content)
            print(f"📄 Created sample PRD file: {file_path}")
        
        # Upload with multipart form data
        data = aiohttp.FormData()
        data.add_field('title', title)
        data.add_field('file', open(file_path, 'rb'), filename=os.path.basename(file_path))
        
        headers = {}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        async with self.session.post(f"{self.base_url}/api/upload/", data=data, headers=headers) as resp:
            result = await resp.json()
            if resp.status == 200 and result.get("status") == "success":
                print(f"✅ Document uploaded: {title}")
                print(f"   Document ID: {result.get('document_id')}")
                print(f"   Authenticated: {result.get('authenticated', False)}")
            else:
                print(f"❌ Upload failed: {result}")
            return result
    
    async def generate_hld_narrative(self, clean_text: str, structured_prd: str, document_id: str = None) -> Dict[str, Any]:
        """Generate HLD narrative."""
        data = {
            "clean_text": clean_text,
            "structured_prd": structured_prd,
            "document_id": document_id
        }
        
        async with self.session.post(f"{self.base_url}/api/hld/narrative", json=data, headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200 and result.get("status") == "success":
                print(f"✅ HLD Narrative generated ({result.get('processing_time_ms', 0)}ms)")
            else:
                print(f"❌ HLD Narrative failed: {result}")
            return result
    
    async def generate_hld_components(self, structured_prd: str, hld_narrative: str, document_id: str = None) -> Dict[str, Any]:
        """Generate HLD components."""
        data = {
            "structured_prd": structured_prd,
            "hld_narrative": hld_narrative,
            "document_id": document_id
        }
        
        async with self.session.post(f"{self.base_url}/api/hld/components", json=data, headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200 and result.get("status") == "success":
                print(f"✅ HLD Components generated ({result.get('processing_time_ms', 0)}ms)")
            else:
                print(f"❌ HLD Components failed: {result}")
            return result
    
    async def generate_complete_lld(self, prd_summary: str, hld_components: list, document_id: str = None) -> Dict[str, Any]:
        """Generate complete LLD."""
        data = {
            "prd_summary": prd_summary,
            "hld_components": hld_components,
            "document_id": document_id
        }
        
        async with self.session.post(f"{self.base_url}/api/lld/complete", json=data, headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200 and result.get("status") == "success":
                print(f"✅ Complete LLD generated ({result.get('processing_time_ms', 0)}ms)")
            else:
                print(f"❌ Complete LLD failed: {result}")
            return result
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """Get user usage statistics."""
        async with self.session.get(f"{self.base_url}/api/auth/usage", headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200:
                print(f"✅ Usage stats fetched")
                print(f"   API Calls: {result.get('total_api_calls', 0)}")
                print(f"   Documents: {result.get('total_documents', 0)}")
            return result
    
    async def get_user_documents(self) -> Dict[str, Any]:
        """Get user documents."""
        async with self.session.get(f"{self.base_url}/api/auth/documents", headers=self.get_headers()) as resp:
            result = await resp.json()
            if resp.status == 200:
                print(f"✅ User documents fetched: {len(result.get('documents', []))} documents")
            return result

async def demo_complete_workflow():
    """Demonstrate complete authentication and database integration workflow."""
    print("🚀 AutoSpec Design - Authentication & Database Integration Demo")
    print("=" * 70)
    
    async with AutoSpecClient() as client:
        # 1. Test health check
        print("\n📋 1. Testing API Health...")
        async with client.session.get(f"{client.base_url}/health") as resp:
            health = await resp.json()
            print(f"✅ API Health: {health.get('status', 'unknown')}")
        
        # 2. Sign up / Login
        print("\n👤 2. User Authentication...")
        test_email = "<EMAIL>"
        await client.signup(
            email=test_email,
            password="password123",
            full_name="Demo User",
            company="AutoSpec Inc"
        )
        
        # 3. Get user profile
        print("\n📊 3. User Profile...")
        profile = await client.get_profile()
        
        # 4. Upload document
        print("\n📄 4. Document Upload...")
        upload_result = await client.upload_document(
            file_path="demo_prd.txt",
            title="E-commerce Platform PRD"
        )
        
        if upload_result.get("status") == "success":
            document_id = upload_result.get("document_id")
            clean_text = upload_result.get("clean_text", "")
            structured_prd = upload_result.get("structured_prd", "E-commerce platform with user auth and payments")
            
            # 5. Generate HLD
            print("\n🏗️ 5. HLD Generation...")
            narrative_result = await client.generate_hld_narrative(
                clean_text=clean_text,
                structured_prd=structured_prd,
                document_id=document_id
            )
            
            if narrative_result.get("status") == "success":
                hld_narrative = narrative_result.get("hld_narrative", "")
                
                components_result = await client.generate_hld_components(
                    structured_prd=structured_prd,
                    hld_narrative=hld_narrative,
                    document_id=document_id
                )
                
                # 6. Generate LLD
                if components_result.get("status") == "success":
                    print("\n🔧 6. LLD Generation...")
                    hld_components = components_result.get("hld_components", [])
                    
                    lld_result = await client.generate_complete_lld(
                        prd_summary=structured_prd,
                        hld_components=hld_components,
                        document_id=document_id
                    )
        
        # 7. Check usage statistics
        print("\n📈 7. Usage Analytics...")
        await client.get_usage_stats()
        
        # 8. Get user documents
        print("\n📚 8. User Documents...")
        await client.get_user_documents()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📋 Summary:")
        print("   ✅ Authentication system working")
        print("   ✅ Database integration working")
        print("   ✅ Document upload with persistence")
        print("   ✅ HLD/LLD generation with tracking")
        print("   ✅ Usage analytics working")
        print("   ✅ User document management")

async def test_anonymous_usage():
    """Test API usage without authentication."""
    print("\n🔓 Testing Anonymous Usage...")
    
    async with AutoSpecClient() as client:
        # Upload without authentication
        upload_result = await client.upload_document(
            file_path="demo_prd_anon.txt",
            title="Anonymous PRD Test"
        )
        
        if upload_result.get("status") == "success":
            print(f"✅ Anonymous upload works: {upload_result.get('authenticated', False)}")
        else:
            print("❌ Anonymous upload failed")

if __name__ == "__main__":
    print("Starting AutoSpec Design Integration Test...")
    
    # Run the demo
    asyncio.run(demo_complete_workflow())
    
    # Test anonymous usage
    asyncio.run(test_anonymous_usage())
    
    print("\n🏁 All tests completed!")
    print("\n💡 Next Steps:")
    print("   1. Check your Supabase database for stored data")
    print("   2. Test the frontend integration")
    print("   3. Configure production environment variables")
    print("   4. Deploy to production")