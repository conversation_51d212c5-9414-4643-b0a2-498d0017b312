"""
HLD Dataflow Generator

Generates dataflow diagrams and flowcharts for system design.
"""
import logging
import json
import re
from typing import Dict, Any, List
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1024,
    together_api_key=TOGETHER_API_KEY,
)

# Dataflow Generation Prompt
DATAFLOW_PROMPT = """
You are an expert system architect. Based on the PRD and HLD components, create a comprehensive dataflow diagram showing how data moves through the system.

Requirements:
1. Generate a Mermaid flowchart diagram
2. Show data flow between components
3. Include user interactions and system processes
4. Use proper Mermaid syntax starting with "flowchart TD"
5. Include decision points and data transformations

Return ONLY the Mermaid flowchart code, no explanations or markdown blocks.

Example format:
flowchart TD
    A[User] --> B[Frontend]
    B --> C[API Gateway]
    C --> D[Authentication Service]
    D --> E[Database]
    E --> D
    D --> C
    C --> B
    B --> A

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

def extract_mermaid_flowchart(llm_response: str) -> str:
    """Extract Mermaid flowchart from LLM response."""
    try:
        # Clean the response
        cleaned = llm_response.strip()
        
        # Remove markdown code blocks
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"```mermaid\n?", "", cleaned)
        cleaned = re.sub(r"```\n?", "", cleaned)
        
        # Look for flowchart pattern
        flowchart_match = re.search(
            r"flowchart\s+(?:TD|TB|LR|RL|BT)\s*\n(.*?)(?:\n\n|\Z)", 
            cleaned, 
            re.DOTALL | re.IGNORECASE
        )
        
        if flowchart_match:
            flowchart_content = flowchart_match.group(0).strip()
            # Ensure it starts with flowchart
            if not flowchart_content.startswith("flowchart"):
                flowchart_content = "flowchart TD\n" + flowchart_content
            return flowchart_content
        
        # If no proper flowchart found, look for any flowchart line
        lines = cleaned.split('\n')
        flowchart_lines = []
        in_flowchart = False
        
        for line in lines:
            line = line.strip()
            if line.lower().startswith('flowchart'):
                in_flowchart = True
                flowchart_lines.append(line)
            elif in_flowchart and line:
                if '-->' in line or '[' in line or '(' in line:
                    flowchart_lines.append(line)
                elif not line.startswith('#') and not line.startswith('*'):
                    flowchart_lines.append(line)
        
        if flowchart_lines:
            return '\n'.join(flowchart_lines)
        
        return get_fallback_dataflow()
        
    except Exception as e:
        logger.error(f"Error extracting Mermaid flowchart: {e}")
        return get_fallback_dataflow()


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_dataflow(prd_summary: str, hld_components: List[Dict]) -> str:
    """Generate dataflow diagram based on PRD and HLD components."""
    logger.info("Generating dataflow diagram...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = DATAFLOW_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        
        response = await llm.ainvoke(prompt)
        logger.info(f"Raw LLM output for dataflow: {response}")
        
        dataflow = extract_mermaid_flowchart(response)
        logger.info("Dataflow diagram generated successfully")
        return dataflow
        
    except Exception as e:
        logger.error(f"Failed to generate dataflow: {str(e)}")
        return get_fallback_dataflow()


def get_fallback_dataflow() -> str:
    """Get fallback dataflow when generation fails."""
    return """flowchart TD
    A[User] --> B[Frontend Application]
    B --> C[API Gateway]
    C --> D[Authentication Service]
    C --> E[Business Logic Service]
    E --> F[Database]
    F --> E
    E --> C
    D --> C
    C --> B
    B --> A
    
    G[Admin User] --> H[Admin Panel]
    H --> C
    
    I[External API] --> C
    C --> J[Notification Service]
    J --> K[Email/SMS Provider]"""
