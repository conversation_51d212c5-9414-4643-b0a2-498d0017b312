"""
HLD Constraints Generator

Generates design constraints and limitations for system architecture.
"""
import logging
import json
import re
from typing import Dict, Any, List
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

logger = logging.getLogger(__name__)

# Initialize LLM
llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1024,
    together_api_key=TOGETHER_API_KEY,
)

# Constraints Generation Prompt
CONSTRAINTS_PROMPT = """
You are an expert system architect. Based on the PRD and HLD components, identify design constraints and limitations that will impact the system architecture.

Requirements:
1. Identify technical constraints (performance, scalability, technology limitations)
2. Identify business constraints (budget, timeline, compliance requirements)
3. Identify operational constraints (maintenance, deployment, monitoring)
4. Identify resource constraints (team size, expertise, infrastructure)
5. Provide impact assessment and mitigation strategies

Return ONLY a valid JSON object, no explanations, no markdown, no code blocks.

Example:
{
  "technical_constraints": [
    {
      "constraint": "Database performance under high load",
      "impact": "Response time degradation",
      "severity": "high",
      "mitigation": "Implement database sharding and read replicas"
    }
  ],
  "business_constraints": [
    {
      "constraint": "Limited budget for cloud infrastructure",
      "impact": "Restricted scaling capabilities",
      "severity": "medium",
      "mitigation": "Use cost-effective cloud services and auto-scaling"
    }
  ],
  "operational_constraints": [
    {
      "constraint": "24/7 availability requirement",
      "impact": "Complex deployment and monitoring needs",
      "severity": "high",
      "mitigation": "Implement blue-green deployment and comprehensive monitoring"
    }
  ],
  "resource_constraints": [
    {
      "constraint": "Small development team",
      "impact": "Limited development velocity",
      "severity": "medium",
      "mitigation": "Use proven frameworks and automated testing"
    }
  ]
}

PRD Summary: {prd_summary}
HLD Components: {hld_components}
"""

def extract_json_object_strict(llm_response: str):
    """Extract JSON object from LLM response with strict validation."""
    try:
        cleaned = llm_response.strip()
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        cleaned = re.sub(r"#+.*", "", cleaned)  # Remove headings
        cleaned = re.sub(
            r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", 
            "", 
            cleaned, 
            flags=re.IGNORECASE
        )
        match = re.search(r"\{.*\}", cleaned, re.DOTALL)
        if not match:
            return {}
        
        json_str = match.group(0)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in LLM response: {json_str}")
            return {}
    except Exception as e:
        logger.error(f"Error extracting JSON from LLM response: {e}")
        logger.error(f"Raw LLM response: {llm_response}")
        return {}


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_constraints(prd_summary: str, hld_components: List[Dict]) -> Dict[str, Any]:
    """Generate design constraints and limitations."""
    logger.info("Generating design constraints...")
    try:
        components_text = json.dumps(hld_components, indent=2)
        prompt = CONSTRAINTS_PROMPT.format(
            prd_summary=prd_summary,
            hld_components=components_text
        )
        
        response = await llm.ainvoke(prompt)
        logger.info(f"Raw LLM output for constraints: {response}")
        
        constraints = extract_json_object_strict(response)
        logger.info("Design constraints generated successfully")
        return constraints
        
    except Exception as e:
        logger.error(f"Failed to generate constraints: {str(e)}")
        return {}


def get_fallback_constraints() -> Dict[str, Any]:
    """Get fallback constraints when generation fails."""
    return {
        "technical_constraints": [
            {
                "constraint": "Database performance under high concurrent users",
                "impact": "Potential response time degradation and system slowdown",
                "severity": "high",
                "mitigation": "Implement database connection pooling, query optimization, and read replicas"
            },
            {
                "constraint": "API rate limiting requirements",
                "impact": "Need to handle throttling and implement retry mechanisms",
                "severity": "medium",
                "mitigation": "Implement exponential backoff and request queuing"
            }
        ],
        "business_constraints": [
            {
                "constraint": "Limited development timeline",
                "impact": "May require prioritization of core features",
                "severity": "high",
                "mitigation": "Use MVP approach and iterative development"
            },
            {
                "constraint": "Compliance requirements (GDPR, CCPA)",
                "impact": "Additional security and privacy implementation overhead",
                "severity": "high",
                "mitigation": "Implement data encryption, audit logs, and user consent management"
            }
        ],
        "operational_constraints": [
            {
                "constraint": "24/7 system availability requirement",
                "impact": "Complex deployment and monitoring infrastructure needed",
                "severity": "high",
                "mitigation": "Implement blue-green deployment, health checks, and automated failover"
            },
            {
                "constraint": "Limited operational team for monitoring",
                "impact": "Reduced ability to respond to incidents quickly",
                "severity": "medium",
                "mitigation": "Implement comprehensive logging, alerting, and automated recovery"
            }
        ],
        "resource_constraints": [
            {
                "constraint": "Small development team (3-5 developers)",
                "impact": "Limited development velocity and knowledge sharing",
                "severity": "medium",
                "mitigation": "Use proven frameworks, implement code reviews, and maintain documentation"
            },
            {
                "constraint": "Budget limitations for cloud infrastructure",
                "impact": "Restricted scaling and redundancy options",
                "severity": "medium",
                "mitigation": "Use cost-effective cloud services and implement auto-scaling policies"
            }
        ]
    }
