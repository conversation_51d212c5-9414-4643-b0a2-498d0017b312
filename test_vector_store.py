#!/usr/bin/env python3
"""
Test Vector Store Directly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.vector_store import vector_store

def test_vector_store():
    print("🔍 Testing Vector Store...")
    
    try:
        # Test adding a document
        document_id = "test-doc-123"
        content = "This is a test document for the e-commerce platform. It contains information about authentication, payment methods, and user management."
        metadata = {
            "title": "Test Document",
            "filename": "test.txt",
            "document_id": document_id,
            "file_size": 100
        }
        
        print("Adding document to vector store...")
        chunks_added = vector_store.add_document(document_id, content, metadata)
        print(f"✅ Added {chunks_added} chunks")
        
        # Test searching
        print("\nSearching for 'authentication'...")
        results = vector_store.search_similar("authentication", limit=3)
        print(f"✅ Found {len(results)} results")
        
        for i, result in enumerate(results):
            print(f"  Result {i+1}: {result.get('content', '')[:50]}...")
        
        # Test listing documents
        print("\nListing documents...")
        documents = vector_store.list_documents()
        print(f"✅ Found {len(documents)} documents")
        
        for doc in documents:
            print(f"  Document: {doc.get('title', 'Unknown')} ({doc.get('chunk_count', 0)} chunks)")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector store test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_vector_store() 