import logging
import json
import re
from app.config import TOGETHER_API_KEY, TOGETHER_MODEL
from langchain_together import Together
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

logger = logging.getLogger(__name__)

llm = Together(
    model=TOGETHER_MODEL,
    temperature=0.2,
    max_tokens=1500,
    together_api_key=TOGETHER_API_KEY,
)

# New, more forgiving prompt
HLD_COMPONENTS_PROMPT = """
You are a senior system architect. Given the Product Requirements Document (PRD) and a high-level design (HLD) narrative, identify the main system components.

For each component, provide:
- name: Component Name
- role: What does it do?
- integration_points: List of other components it interacts with
- design_rationale: Why is it needed?

Guidelines:
- List at least 4 components. If unsure, make your best guess based on the PRD and narrative.
- Do not include explanations, markdown, or code blocks. Just return a JSON array as shown in the example.
- If you cannot find 4, invent plausible ones based on typical system design for the PRD.

Example:
[
  {
    "name": "User Service",
    "role": "Handles user authentication and account management",
    "integration_points": ["API Gateway", "Database"],
    "design_rationale": "Manages user accounts, authentication, and security"
  },
  {
    "name": "Product Catalog",
    "role": "Manages product listings and inventory",
    "integration_points": ["API Gateway", "Database"],
    "design_rationale": "Handles product-related operations and inventory"
  },
  {
    "name": "Order Service",
    "role": "Handles order placement and tracking",
    "integration_points": ["API Gateway", "User Service", "Product Catalog", "Payment Gateway"],
    "design_rationale": "Processes customer orders and manages order lifecycle"
  },
  {
    "name": "Payment Gateway",
    "role": "Processes payments securely",
    "integration_points": ["API Gateway", "Order Service"],
    "design_rationale": "Handles secure payment processing and integration with external providers"
  }
]

PRD:
{structured_prd}

HLD Narrative:
{hld_narrative}
"""

# Fallback default components (for e-commerce)
DEFAULT_COMPONENTS = [
    {
        "name": "User Service",
        "role": "Handles user authentication and account management",
        "integration_points": ["API Gateway", "Database"],
        "design_rationale": "Manages user accounts, authentication, and security"
    },
    {
        "name": "Product Catalog",
        "role": "Manages product listings and inventory",
        "integration_points": ["API Gateway", "Database"],
        "design_rationale": "Handles product-related operations and inventory"
    },
    {
        "name": "Order Service",
        "role": "Handles order placement and tracking",
        "integration_points": ["API Gateway", "User Service", "Product Catalog", "Payment Gateway"],
        "design_rationale": "Processes customer orders and manages order lifecycle"
    },
    {
        "name": "Payment Gateway",
        "role": "Processes payments securely",
        "integration_points": ["API Gateway", "Order Service"],
        "design_rationale": "Handles secure payment processing and integration with external providers"
    }
]

def extract_first_json_array(text: str):
    """Extract the first valid JSON array from text, even if surrounded by extra text."""
    try:
        cleaned = text.strip()
        # Remove markdown/code blocks
        cleaned = re.sub(r"```[\s\S]*?```", "", cleaned)
        # Remove headings and common LLM filler
        cleaned = re.sub(r"#+.*", "", cleaned)
        cleaned = re.sub(r"(The final answer is:|Step \d+:|Boxed answer:|Final answer:).*", "", cleaned, flags=re.IGNORECASE)
        # Find the first JSON array
        match = re.search(r"\[.*?\]", cleaned, re.DOTALL)
        if not match:
            logger.error(f"No JSON array found in LLM output. Raw response: {text}")
            return None
        json_str = match.group(0)
        try:
            parsed = json.loads(json_str)
            if isinstance(parsed, list) and len(parsed) > 0:
                return parsed
            else:
                logger.error(f"Parsed JSON array is empty or not a list. Raw response: {text}")
                return None
        except Exception as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"JSON string: {json_str}")
            logger.error(f"Raw LLM response: {text}")
            return None
    except Exception as e:
        logger.error(f"Error during JSON array extraction: {str(e)}")
        logger.error(f"Raw LLM response: {text}")
        return None

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type(Exception),
)
async def generate_hld_components(structured_prd: str, hld_narrative: str):
    logger.info("Generating HLD Components (fresh implementation)...")
    try:
        prompt = HLD_COMPONENTS_PROMPT.format(
            structured_prd=structured_prd, hld_narrative=hld_narrative
        )
        logger.info("Sending request to Together AI...")
        result = await llm.ainvoke(prompt)
        logger.info("Raw LLM output received:")
        logger.info(result)
        print("[DEBUG] Raw LLM output for components (fresh):", result)
        components = extract_first_json_array(result)
        if components is not None:
            logger.info("Parsed HLD components successfully (fresh).")
            return components
        else:
            logger.warning("LLM output invalid or empty, returning default components.")
            return DEFAULT_COMPONENTS
    except Exception as e:
        print("[DEBUG] Outer exception in generate_hld_components (fresh):", repr(e))
        logger.error(f"Failed to generate HLD components (fresh): {e}")
        logger.info("Returning default components (fresh)...")
        return DEFAULT_COMPONENTS
