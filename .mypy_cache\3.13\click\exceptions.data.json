{".class": "MypyFile", "_fullname": "click.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Abort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.Abort", "name": "Abort", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.Abort", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.Abort", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.Abort.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.Abort", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadArgumentUsage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.UsageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.BadArgumentUsage", "name": "BadArgumentUsage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.BadArgumentUsage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.BadArgumentUsage", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.BadArgumentUsage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.BadArgumentUsage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadOptionUsage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.UsageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.BadOptionUsage", "name": "BadOptionUsage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.BadOptionUsage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.BadOptionUsage", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "option_name", "message", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.BadOptionUsage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "option_name", "message", "ctx"], "arg_types": ["click.exceptions.BadOptionUsage", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadOptionUsage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.BadOptionUsage.option_name", "name": "option_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.BadOptionUsage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.BadOptionUsage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.UsageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.BadParameter", "name": "BadParameter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.BadParameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.BadParameter", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "ctx", "param", "param_hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.BadParameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "message", "ctx", "param", "param_hint"], "arg_types": ["click.exceptions.BadParameter", "builtins.str", {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["click.core.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.BadParameter.format_message", "name": "format_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.exceptions.BadParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_message of BadParameter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "param": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.BadParameter.param", "name": "param", "type": {".class": "UnionType", "items": ["click.core.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "param_hint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.BadParameter.param_hint", "name": "param_hint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.BadParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.BadParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClickException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.ClickException", "name": "ClickException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.ClickException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.ClickException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["click.exceptions.ClickException", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClickException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.ClickException.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["click.exceptions.ClickException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of ClickException", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exit_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "click.exceptions.ClickException.exit_code", "name": "exit_code", "type": "builtins.int"}}, "format_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.ClickException.format_message", "name": "format_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.exceptions.ClickException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_message of ClickException", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.ClickException.message", "name": "message", "type": "builtins.str"}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.ClickException.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "file"], "arg_types": ["click.exceptions.ClickException", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of ClickException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_color": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.exceptions.ClickException.show_color", "name": "show_color", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.ClickException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.ClickException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Command": {".class": "SymbolTableNode", "cross_ref": "click.core.Command", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "click.core.Context", "kind": "Gdef"}, "Exit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.Exit", "name": "Exit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.Exit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.Exit", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.Exit.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "code"], "arg_types": ["click.exceptions.Exit", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Exit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "click.exceptions.Exit.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "exit_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.exceptions.Exit.exit_code", "name": "exit_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.Exit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.Exit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.ClickException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.FileError", "name": "FileError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.FileError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.FileError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "filename", "hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.FileError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "filename", "hint"], "arg_types": ["click.exceptions.FileError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.FileError.filename", "name": "filename", "type": "builtins.str"}}, "format_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.FileError.format_message", "name": "format_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.exceptions.FileError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_message of FileError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ui_filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.exceptions.FileError.ui_filename", "name": "ui_filename", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.FileError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.FileError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.BadParameter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.MissingParameter", "name": "MissingParameter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.MissingParameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.MissingParameter", "click.exceptions.BadParameter", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "message", "ctx", "param", "param_hint", "param_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.MissingParameter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "message", "ctx", "param", "param_hint", "param_type"], "arg_types": ["click.exceptions.MissingParameter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["click.core.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingParameter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.MissingParameter.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["click.exceptions.MissingParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of MissingParameter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.MissingParameter.format_message", "name": "format_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.exceptions.MissingParameter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_message of MissingParameter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "param_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.MissingParameter.param_type", "name": "param_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.MissingParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.MissingParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoArgsIsHelpError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.UsageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.NoArgsIsHelpError", "name": "NoArgsIsHelpError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.NoArgsIsHelpError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.NoArgsIsHelpError", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.NoArgsIsHelpError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "arg_types": ["click.exceptions.NoArgsIsHelpError", "click.core.Context"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoArgsIsHelpError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.exceptions.NoArgsIsHelpError.ctx", "name": "ctx", "type": "click.core.Context"}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.NoArgsIsHelpError.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "file"], "arg_types": ["click.exceptions.NoArgsIsHelpError", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of NoArgsIsHelpError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.NoArgsIsHelpError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.NoArgsIsHelpError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.UsageError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.NoSuchOption", "name": "NoSuchOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.NoSuchOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.NoSuchOption", "click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "option_name", "message", "possibilities", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.NoSuchOption.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "option_name", "message", "possibilities", "ctx"], "arg_types": ["click.exceptions.NoSuchOption", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSuchOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.NoSuchOption.format_message", "name": "format_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.exceptions.NoSuchOption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_message of NoSuchOption", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.NoSuchOption.option_name", "name": "option_name", "type": "builtins.str"}}, "possibilities": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.NoSuchOption.possibilities", "name": "possibilities", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.NoSuchOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.NoSuchOption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "click.core.Parameter", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click.exceptions.ClickException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.exceptions.UsageError", "name": "UsageError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.exceptions.UsageError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.exceptions", "mro": ["click.exceptions.UsageError", "click.exceptions.ClickException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.UsageError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "ctx"], "arg_types": ["click.exceptions.UsageError", "builtins.str", {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UsageError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cmd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.exceptions.UsageError.cmd", "name": "cmd", "type": {".class": "UnionType", "items": ["click.core.Command", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.exceptions.UsageError.ctx", "name": "ctx", "type": {".class": "UnionType", "items": ["click.core.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exit_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "click.exceptions.UsageError.exit_code", "name": "exit_code", "type": "builtins.int"}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions.UsageError.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "file"], "arg_types": ["click.exceptions.UsageError", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of UsageError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.exceptions.UsageError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.exceptions.UsageError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_": {".class": "SymbolTableNode", "cross_ref": "gettext.gettext", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_join_param_hints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["param_hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.exceptions._join_param_hints", "name": "_join_param_hints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["param_hint"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_join_param_hints", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cabc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "echo": {".class": "SymbolTableNode", "cross_ref": "click.utils.echo", "kind": "Gdef"}, "format_filename": {".class": "SymbolTableNode", "cross_ref": "click.utils.format_filename", "kind": "Gdef"}, "get_text_stderr": {".class": "SymbolTableNode", "cross_ref": "click._compat.get_text_stderr", "kind": "Gdef"}, "ngettext": {".class": "SymbolTableNode", "cross_ref": "gettext.ngettext", "kind": "Gdef"}, "resolve_color_default": {".class": "SymbolTableNode", "cross_ref": "click.globals.resolve_color_default", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\AutoSpecDesign\\venv\\Lib\\site-packages\\click\\exceptions.py"}