import logging
from fastapi import APIRouter, Request
from app.hld.hld_narrative import generate_hld_narrative
from app.hld.hld_components import generate_hld_components
from app.hld.hld_sequence import generate_sequence_diagram
from app.hld.hld_dataflow import generate_dataflow, get_fallback_dataflow
from app.hld.hld_tech_stack import generate_tech_stack, get_fallback_tech_stack
from app.hld.hld_constraints import generate_constraints, get_fallback_constraints
from app.hld.hld_assumptions import generate_assumptions, get_fallback_assumptions
from app.hld.hld_issues import generate_issues, get_fallback_issues
from app.auth.auth_guard import OptionalUser
import time

logger = logging.getLogger(__name__)

router = APIRouter()


# HLD Narrative Route
@router.post("/narrative")
async def hld_narrative_route(request: Request):
    start_time = time.time()
    
    try:
        body = await request.json()
        clean_text = body.get("clean_text")
        structured_prd = body.get("structured_prd")
        # document_id = body.get("document_id")  # TODO: Use for Supabase integration

        if not clean_text or not structured_prd:
            return {
                "status": "error",
                "message": "Missing clean_text or structured_prd in request body.",
            }

        summary = await generate_hld_narrative(structured_prd, clean_text)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "hld_narrative": summary,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# HLD Components Route
@router.post("/components")
async def hld_components_route(request: Request):
    start_time = time.time()
    
    try:
        body = await request.json()
        structured_prd = body.get("structured_prd")
        hld_narrative = body.get("hld_narrative")
        # document_id = body.get("document_id")  # TODO: Use for Supabase integration

        if not structured_prd or not hld_narrative:
            return {
                "status": "error",
                "message": "Missing structured_prd or hld_narrative in request body.",
            }

        components = await generate_hld_components(structured_prd, hld_narrative)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success",
            "hld_components": components,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Sequence Diagram Route
@router.post("/sequence")
async def sequence_diagram_route(request: Request):
    start_time = time.time()

    try:
        body = await request.json()
        structured_prd = body.get("structured_prd")
        hld_narrative = body.get("hld_narrative")
        # document_id = body.get("document_id")  # TODO: Use for Supabase integration

        if not structured_prd or not hld_narrative:
            return {
                "status": "error",
                "message": "Missing structured_prd or hld_narrative in request body.",
            }

        mermaid = await generate_sequence_diagram(structured_prd, hld_narrative)
        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success",
            "mermaid": mermaid,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Dataflow Route
@router.post("/dataflow")
async def dataflow_route(request: Request):
    """Generate dataflow diagram based on PRD and HLD components."""
    start_time = time.time()

    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        try:
            dataflow = await generate_dataflow(prd_summary, hld_components)
        except Exception as e:
            logger.warning(f"Dataflow generation failed, using fallback: {e}")
            dataflow = get_fallback_dataflow()

        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success",
            "dataflow": dataflow,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Constraints Route
@router.post("/constraints")
async def constraints_route(request: Request):
    """Generate design constraints based on PRD and HLD components."""
    start_time = time.time()

    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        try:
            constraints = await generate_constraints(prd_summary, hld_components)
        except Exception as e:
            logger.warning(f"Constraints generation failed, using fallback: {e}")
            constraints = get_fallback_constraints()

        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success",
            "constraints": constraints,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Tech Stack Route
@router.post("/tech-stack")
async def tech_stack_route(request: Request):
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        try:
            tech_stack = await generate_tech_stack(prd_summary, hld_components)
        except Exception as e:
            logger.warning(f"Tech stack generation failed, using fallback: {e}")
            tech_stack = get_fallback_tech_stack()

        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "tech_stack": tech_stack,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Assumptions Route
@router.post("/assumptions")
async def assumptions_route(request: Request):
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        try:
            assumptions = await generate_assumptions(prd_summary, hld_components)
        except Exception as e:
            logger.warning(f"Assumptions generation failed, using fallback: {e}")
            assumptions = get_fallback_assumptions()

        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "assumptions": assumptions,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}


# Issues Route
@router.post("/issues")
async def issues_route(request: Request):
    start_time = time.time()
    
    try:
        body = await request.json()
        prd_summary = body.get("prd_summary")
        hld_components = body.get("hld_components")

        if not prd_summary or not hld_components:
            return {
                "status": "error",
                "message": "Missing prd_summary or hld_components in request body.",
            }

        try:
            issues = await generate_issues(prd_summary, hld_components)
        except Exception as e:
            logger.warning(f"Issues generation failed, using fallback: {e}")
            issues = get_fallback_issues()

        processing_time_ms = int((time.time() - start_time) * 1000)

        return {
            "status": "success", 
            "issues": issues,
            "processing_time_ms": processing_time_ms
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}
