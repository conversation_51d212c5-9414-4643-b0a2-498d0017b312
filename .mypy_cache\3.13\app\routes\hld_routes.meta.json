{"data_mtime": 1753995170, "dep_lines": [3, 4, 5, 6, 7, 8, 9, 10, 11, 1, 2, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.hld.hld_narrative", "app.hld.hld_components", "app.hld.hld_sequence", "app.hld.hld_dataflow", "app.hld.hld_tech_stack", "app.hld.hld_constraints", "app.hld.hld_assumptions", "app.hld.hld_issues", "app.auth.auth_guard", "logging", "<PERSON><PERSON><PERSON>", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "contextlib", "enum", "fastapi.params", "fastapi.routing", "starlette", "starlette.requests", "starlette.responses", "starlette.routing", "types", "typing", "app.hld"], "hash": "ba9668125564b3c39dd24da6a1f3ee7f6f598645", "id": "app.routes.hld_routes", "ignore_all": false, "interface_hash": "413668c68bf1f0e6157f8705a31695ff2e2acc1d", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\AutoSpecDesign\\app\\routes\\hld_routes.py", "plugin_data": null, "size": 8888, "suppressed": [], "version_id": "1.15.0"}