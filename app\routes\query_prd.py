import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from app.services.rag_service import rag_service
from app.auth.auth_guard import OptionalUser
from app.services.supabase_service import supabase_service
import time

router = APIRouter()
logger = logging.getLogger(__name__)

# Request models
class QueryRequest(BaseModel):
    question: str
    document_id: str = None
    limit: int = 5

class SearchRequest(BaseModel):
    query: str
    limit: int = 3

@router.post("/")
async def query_documents(request: QueryRequest, user = OptionalUser):
    """Query documents using RAG (Retrieval-Augmented Generation)."""
    start_time = time.time()
    
    try:
        # Filter by user's documents if authenticated
        document_id = request.document_id
        if user and supabase_service.is_available() and not document_id:
            try:
                user_docs = await supabase_service.get_user_documents(user["user_id"])
                # Could implement logic to search only user's documents
            except Exception as e:
                logger.warning(f"Failed to get user documents: {e}")
        
        result = await rag_service.query_documents(
            question=request.question,
            limit=request.limit,
            document_id=document_id
        )
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        result["processing_time_ms"] = processing_time_ms
        
        # Log API usage if authenticated
        if user and supabase_service.is_available():
            try:
                await supabase_service.log_api_usage(
                    user_id=user["user_id"],
                    endpoint="/api/query/",
                    processing_time_ms=processing_time_ms
                )
            except Exception as e:
                logger.warning(f"Failed to log API usage: {e}")
        
        return result
    except Exception as e:
        logger.error(f"Query failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search-only")
async def search_documents_only(request: SearchRequest, user = OptionalUser):
    """Search documents without AI generation (debugging endpoint)."""
    start_time = time.time()
    
    try:
        results = await rag_service.search_documents(
            query=request.query,
            limit=request.limit
        )
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        # Log API usage if authenticated
        if user and supabase_service.is_available():
            try:
                await supabase_service.log_api_usage(
                    user_id=user["user_id"],
                    endpoint="/api/query/search-only",
                    processing_time_ms=processing_time_ms
                )
            except Exception as e:
                logger.warning(f"Failed to log API usage: {e}")
        
        return {
            "status": "success",
            "query": request.query,
            "results": results,
            "processing_time_ms": processing_time_ms
        }
    except Exception as e:
        logger.error(f"Search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents")
async def list_documents(user = OptionalUser):
    """List available documents in the vector store."""
    try:
        documents = rag_service.list_documents()
        
        # If user is authenticated, also include their database documents
        if user and supabase_service.is_available():
            try:
                user_docs = await supabase_service.get_user_documents(user["user_id"])
                return {
                    "status": "success",
                    "vector_store_documents": documents,
                    "user_documents": user_docs
                }
            except Exception as e:
                logger.warning(f"Failed to get user documents: {e}")
        
        return {
            "status": "success",
            "documents": documents
        }
    except Exception as e:
        logger.error(f"List documents failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/documents/{document_id}/chunks")
async def get_document_chunks(document_id: str, user = OptionalUser):
    """Get chunks for a specific document."""
    try:
        chunks = rag_service.get_document_chunks(document_id)
        
        # Log API usage if authenticated
        if user and supabase_service.is_available():
            try:
                await supabase_service.log_api_usage(
                    user_id=user["user_id"],
                    endpoint=f"/api/query/documents/{document_id}/chunks",
                    processing_time_ms=50  # Estimated for simple DB query
                )
            except Exception as e:
                logger.warning(f"Failed to log API usage: {e}")
        
        return {
            "status": "success",
            "document_id": document_id,
            "chunks": chunks
        }
    except Exception as e:
        logger.error(f"Get document chunks failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/documents/{document_id}")
async def delete_document(document_id: str, user = OptionalUser):
    """Delete a document from the vector store."""
    try:
        success = rag_service.delete_document(document_id)
        
        if success:
            # Also delete from database if user is authenticated
            if user and supabase_service.is_available():
                try:
                    # Update document status to 'archived' instead of deleting
                    await supabase_service.update_document(
                        document_id=document_id,
                        status="archived"
                    )
                    
                    await supabase_service.log_api_usage(
                        user_id=user["user_id"],
                        endpoint=f"/api/query/documents/{document_id}",
                        processing_time_ms=100
                    )
                except Exception as e:
                    logger.warning(f"Failed to update document in database: {e}")
            
            return {
                "status": "success",
                "message": f"Document {document_id} deleted successfully"
            }
        else:
            return {
                "status": "error",
                "message": f"Failed to delete document {document_id}"
            }
    except Exception as e:
        logger.error(f"Delete document failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """Check RAG system health."""
    try:
        health_info = await rag_service.health_check()
        return health_info
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
