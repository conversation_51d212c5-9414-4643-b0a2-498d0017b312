"""
Storage Service

Handles file storage operations using Supabase Storage or local filesystem as fallback.
"""
import logging
import os
import shutil
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class StorageService:
    """
    Service class for handling file storage operations.
    
    This service provides a fallback mechanism when Supabase Storage is not available,
    using local filesystem storage instead.
    """
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.local_storage_path = os.getenv("LOCAL_STORAGE_PATH", "storage")
        
        self._client = None
        self._available = False
        
        # Ensure local storage directory exists
        os.makedirs(self.local_storage_path, exist_ok=True)
        
        # Initialize Supabase storage client if credentials are available
        if self.supabase_url and self.supabase_key:
            try:
                from supabase import create_client, Client
                self._client: Client = create_client(self.supabase_url, self.supabase_key)
                self._available = True
                logger.info("Supabase storage client initialized successfully")
            except ImportError:
                logger.warning("Supabase library not available. Using local storage.")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase storage client: {e}")
        else:
            logger.info("Supabase credentials not provided. Using local storage.")
    
    def is_available(self) -> bool:
        """Check if Supabase Storage is available and configured."""
        return self._available and self._client is not None
    
    async def upload_file(
        self, 
        bucket_name: str, 
        file_path: str, 
        file_content: bytes,
        content_type: str = "application/octet-stream"
    ) -> Dict[str, Any]:
        """Upload file to storage."""
        if self.is_available():
            return await self._upload_to_supabase(bucket_name, file_path, file_content, content_type)
        else:
            return await self._upload_to_local(bucket_name, file_path, file_content)
    
    async def download_file(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Download file from storage."""
        if self.is_available():
            return await self._download_from_supabase(bucket_name, file_path)
        else:
            return await self._download_from_local(bucket_name, file_path)
    
    async def delete_file(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Delete file from storage."""
        if self.is_available():
            return await self._delete_from_supabase(bucket_name, file_path)
        else:
            return await self._delete_from_local(bucket_name, file_path)
    
    async def get_file_url(self, bucket_name: str, file_path: str) -> Optional[str]:
        """Get public URL for file."""
        if self.is_available():
            try:
                response = self._client.storage.from_(bucket_name).get_public_url(file_path)
                return response
            except Exception as e:
                logger.error(f"Failed to get file URL from Supabase: {e}")
                return None
        else:
            # For local storage, return a local file path
            local_path = os.path.join(self.local_storage_path, bucket_name, file_path)
            return local_path if os.path.exists(local_path) else None
    
    async def _upload_to_supabase(
        self, 
        bucket_name: str, 
        file_path: str, 
        file_content: bytes,
        content_type: str
    ) -> Dict[str, Any]:
        """Upload file to Supabase Storage."""
        try:
            response = self._client.storage.from_(bucket_name).upload(
                file_path, 
                file_content,
                file_options={"content-type": content_type}
            )
            
            if response.get("error"):
                return {"error": response["error"]["message"]}
            
            return {
                "success": True,
                "path": file_path,
                "bucket": bucket_name,
                "storage_type": "supabase"
            }
        except Exception as e:
            logger.error(f"Failed to upload to Supabase Storage: {e}")
            return {"error": str(e)}
    
    async def _upload_to_local(
        self, 
        bucket_name: str, 
        file_path: str, 
        file_content: bytes
    ) -> Dict[str, Any]:
        """Upload file to local storage."""
        try:
            # Create bucket directory if it doesn't exist
            bucket_dir = os.path.join(self.local_storage_path, bucket_name)
            os.makedirs(bucket_dir, exist_ok=True)
            
            # Create file path directory if needed
            full_path = os.path.join(bucket_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            # Write file
            with open(full_path, 'wb') as f:
                f.write(file_content)
            
            return {
                "success": True,
                "path": file_path,
                "bucket": bucket_name,
                "local_path": full_path,
                "storage_type": "local"
            }
        except Exception as e:
            logger.error(f"Failed to upload to local storage: {e}")
            return {"error": str(e)}
    
    async def _download_from_supabase(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Download file from Supabase Storage."""
        try:
            response = self._client.storage.from_(bucket_name).download(file_path)
            
            if response.get("error"):
                return {"error": response["error"]["message"]}
            
            return {
                "success": True,
                "content": response,
                "storage_type": "supabase"
            }
        except Exception as e:
            logger.error(f"Failed to download from Supabase Storage: {e}")
            return {"error": str(e)}
    
    async def _download_from_local(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Download file from local storage."""
        try:
            full_path = os.path.join(self.local_storage_path, bucket_name, file_path)
            
            if not os.path.exists(full_path):
                return {"error": "File not found"}
            
            with open(full_path, 'rb') as f:
                content = f.read()
            
            return {
                "success": True,
                "content": content,
                "storage_type": "local"
            }
        except Exception as e:
            logger.error(f"Failed to download from local storage: {e}")
            return {"error": str(e)}
    
    async def _delete_from_supabase(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Delete file from Supabase Storage."""
        try:
            response = self._client.storage.from_(bucket_name).remove([file_path])
            
            if response.get("error"):
                return {"error": response["error"]["message"]}
            
            return {
                "success": True,
                "storage_type": "supabase"
            }
        except Exception as e:
            logger.error(f"Failed to delete from Supabase Storage: {e}")
            return {"error": str(e)}
    
    async def _delete_from_local(self, bucket_name: str, file_path: str) -> Dict[str, Any]:
        """Delete file from local storage."""
        try:
            full_path = os.path.join(self.local_storage_path, bucket_name, file_path)
            
            if not os.path.exists(full_path):
                return {"error": "File not found"}
            
            os.remove(full_path)
            
            return {
                "success": True,
                "storage_type": "local"
            }
        except Exception as e:
            logger.error(f"Failed to delete from local storage: {e}")
            return {"error": str(e)}

# Global instance
storage_service = StorageService()
