services:
  - type: web
    name: autospec-design-backend
    runtime: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: TOGETHER_API_KEY
        sync: false
      - key: TOGETHER_MODEL
        value: meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: QDRANT_URL
        value: http://localhost:6333
      - key: ENVIRONMENT
        value: production 